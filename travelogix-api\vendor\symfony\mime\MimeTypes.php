<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Mime;

use Symfony\Component\Mime\Exception\LogicException;

/**
 * Manages MIME types and file extensions.
 *
 * For MIME type guessing, you can register custom guessers
 * by calling the registerGuesser() method.
 * Custom guessers are always called before any default ones:
 *
 *     $guesser = new MimeTypes();
 *     $guesser->registerGuesser(new MyCustomMimeTypeGuesser());
 *
 * If you want to change the order of the default guessers, just re-register your
 * preferred one as a custom one. The last registered guesser is preferred over
 * previously registered ones.
 *
 * Re-registering a built-in guesser also allows you to configure it:
 *
 *     $guesser = new MimeTypes();
 *     $guesser->registerGuesser(new FileinfoMimeTypeGuesser('/path/to/magic/file'));
 *
 * <AUTHOR> <<EMAIL>>
 */
final class MimeTypes implements MimeTypesInterface
{
    private array $extensions = [];
    private array $mimeTypes = [];

    /**
     * @var MimeTypeGuesserInterface[]
     */
    private array $guessers = [];
    private static MimeTypes $default;

    public function __construct(array $map = [])
    {
        foreach ($map as $mimeType => $extensions) {
            $this->extensions[$mimeType] = $extensions;

            foreach ($extensions as $extension) {
                $this->mimeTypes[$extension][] = $mimeType;
            }
        }
        $this->registerGuesser(new FileBinaryMimeTypeGuesser());
        $this->registerGuesser(new FileinfoMimeTypeGuesser());
    }

    public static function setDefault(self $default): void
    {
        self::$default = $default;
    }

    public static function getDefault(): self
    {
        return self::$default ??= new self();
    }

    /**
     * Registers a MIME type guesser.
     *
     * The last registered guesser has precedence over the other ones.
     */
    public function registerGuesser(MimeTypeGuesserInterface $guesser): void
    {
        array_unshift($this->guessers, $guesser);
    }

    public function getExtensions(string $mimeType): array
    {
        if ($this->extensions) {
            $extensions = $this->extensions[$mimeType] ?? $this->extensions[$lcMimeType = strtolower($mimeType)] ?? null;
        }

        return $extensions ?? self::MAP[$mimeType] ?? self::MAP[$lcMimeType ?? strtolower($mimeType)] ?? [];
    }

    public function getMimeTypes(string $ext): array
    {
        if ($this->mimeTypes) {
            $mimeTypes = $this->mimeTypes[$ext] ?? $this->mimeTypes[$lcExt = strtolower($ext)] ?? null;
        }

        return $mimeTypes ?? self::REVERSE_MAP[$ext] ?? self::REVERSE_MAP[$lcExt ?? strtolower($ext)] ?? [];
    }

    public function isGuesserSupported(): bool
    {
        foreach ($this->guessers as $guesser) {
            if ($guesser->isGuesserSupported()) {
                return true;
            }
        }

        return false;
    }

    /**
     * The file is passed to each registered MIME type guesser in reverse order
     * of their registration (last registered is queried first). Once a guesser
     * returns a value that is not null, this method terminates and returns the
     * value.
     */
    public function guessMimeType(string $path): ?string
    {
        foreach ($this->guessers as $guesser) {
            if (!$guesser->isGuesserSupported()) {
                continue;
            }

            if (null !== $mimeType = $guesser->guessMimeType($path)) {
                return $mimeType;
            }
        }

        if (!$this->isGuesserSupported()) {
            throw new LogicException('Unable to guess the MIME type as no guessers are available (have you enabled the php_fileinfo extension?).');
        }

        return null;
    }

    /**
     * A map of MIME types and their default extensions.
     *
     * Updated from upstream on 2024-11-09.
     *
     * @see Resources/bin/update_mime_types.php
     */
    private const MAP = [
        'application/acrobat' => ['pdf'],
        'application/andrew-inset' => ['ez'],
        'application/annodex' => ['anx'],
        'application/appinstaller' => ['appinstaller'],
        'application/applixware' => ['aw'],
        'application/appx' => ['appx'],
        'application/appxbundle' => ['appxbundle'],
        'application/atom+xml' => ['atom'],
        'application/atomcat+xml' => ['atomcat'],
        'application/atomdeleted+xml' => ['atomdeleted'],
        'application/atomsvc+xml' => ['atomsvc'],
        'application/atsc-dwd+xml' => ['dwd'],
        'application/atsc-held+xml' => ['held'],
        'application/atsc-rsat+xml' => ['rsat'],
        'application/automationml-aml+xml' => ['aml'],
        'application/automationml-amlx+zip' => ['amlx'],
        'application/bat' => ['bat'],
        'application/bdoc' => ['bdoc'],
        'application/bzip2' => ['bz2', 'bz'],
        'application/calendar+xml' => ['xcs'],
        'application/cbor' => ['cbor'],
        'application/ccxml+xml' => ['ccxml'],
        'application/cdfx+xml' => ['cdfx'],
        'application/cdmi-capability' => ['cdmia'],
        'application/cdmi-container' => ['cdmic'],
        'application/cdmi-domain' => ['cdmid'],
        'application/cdmi-object' => ['cdmio'],
        'application/cdmi-queue' => ['cdmiq'],
        'application/cdr' => ['cdr'],
        'application/coreldraw' => ['cdr'],
        'application/cpl+xml' => ['cpl'],
        'application/csv' => ['csv'],
        'application/cu-seeme' => ['cu'],
        'application/cwl' => ['cwl'],
        'application/dash+xml' => ['mpd'],
        'application/dash-patch+xml' => ['mpp'],
        'application/davmount+xml' => ['davmount'],
        'application/dbase' => ['dbf'],
        'application/dbf' => ['dbf'],
        'application/dicom' => ['dcm'],
        'application/docbook+xml' => ['dbk', 'docbook'],
        'application/dssc+der' => ['dssc'],
        'application/dssc+xml' => ['xdssc'],
        'application/ecmascript' => ['ecma', 'es'],
        'application/emf' => ['emf'],
        'application/emma+xml' => ['emma'],
        'application/emotionml+xml' => ['emotionml'],
        'application/epub+zip' => ['epub'],
        'application/exi' => ['exi'],
        'application/express' => ['exp'],
        'application/fdf' => ['fdf'],
        'application/fdt+xml' => ['fdt'],
        'application/fits' => ['fits', 'fit', 'fts'],
        'application/font-tdpfr' => ['pfr'],
        'application/font-woff' => ['woff'],
        'application/futuresplash' => ['swf', 'spl'],
        'application/geo+json' => ['geojson', 'geo.json'],
        'application/gml+xml' => ['gml'],
        'application/gnunet-directory' => ['gnd'],
        'application/gpx' => ['gpx'],
        'application/gpx+xml' => ['gpx'],
        'application/gxf' => ['gxf'],
        'application/gzip' => ['gz'],
        'application/hjson' => ['hjson'],
        'application/hta' => ['hta'],
        'application/hyperstudio' => ['stk'],
        'application/ico' => ['ico'],
        'application/ics' => ['vcs', 'ics', 'ifb', 'icalendar'],
        'application/illustrator' => ['ai'],
        'application/inkml+xml' => ['ink', 'inkml'],
        'application/ipfix' => ['ipfix'],
        'application/its+xml' => ['its'],
        'application/java' => ['class'],
        'application/java-archive' => ['jar', 'war', 'ear'],
        'application/java-byte-code' => ['class'],
        'application/java-serialized-object' => ['ser'],
        'application/java-vm' => ['class'],
        'application/javascript' => ['js', 'jsm', 'mjs'],
        'application/jrd+json' => ['jrd'],
        'application/json' => ['json', 'map'],
        'application/json-patch+json' => ['json-patch'],
        'application/json5' => ['json5'],
        'application/jsonml+json' => ['jsonml'],
        'application/ld+json' => ['jsonld'],
        'application/lgr+xml' => ['lgr'],
        'application/lost+xml' => ['lostxml'],
        'application/lotus123' => ['123', 'wk1', 'wk3', 'wk4', 'wks'],
        'application/m3u' => ['m3u', 'm3u8', 'vlc'],
        'application/mac-binhex40' => ['hqx'],
        'application/mac-compactpro' => ['cpt'],
        'application/mads+xml' => ['mads'],
        'application/manifest+json' => ['webmanifest'],
        'application/marc' => ['mrc'],
        'application/marcxml+xml' => ['mrcx'],
        'application/mathematica' => ['ma', 'nb', 'mb'],
        'application/mathml+xml' => ['mathml', 'mml'],
        'application/mbox' => ['mbox'],
        'application/mdb' => ['mdb'],
        'application/media-policy-dataset+xml' => ['mpf'],
        'application/mediaservercontrol+xml' => ['mscml'],
        'application/metalink+xml' => ['metalink'],
        'application/metalink4+xml' => ['meta4'],
        'application/mets+xml' => ['mets'],
        'application/microsoftpatch' => ['msp'],
        'application/microsoftupdate' => ['msu'],
        'application/mmt-aei+xml' => ['maei'],
        'application/mmt-usd+xml' => ['musd'],
        'application/mods+xml' => ['mods'],
        'application/mp21' => ['m21', 'mp21'],
        'application/mp4' => ['mp4', 'mpg4', 'mp4s', 'm4p'],
        'application/mrb-consumer+xml' => ['xdf'],
        'application/mrb-publish+xml' => ['xdf'],
        'application/ms-tnef' => ['tnef', 'tnf'],
        'application/msaccess' => ['mdb'],
        'application/msexcel' => ['xls', 'xlc', 'xll', 'xlm', 'xlw', 'xla', 'xlt', 'xld'],
        'application/msix' => ['msix'],
        'application/msixbundle' => ['msixbundle'],
        'application/mspowerpoint' => ['ppz', 'ppt', 'pps', 'pot'],
        'application/msword' => ['doc', 'dot'],
        'application/msword-template' => ['dot'],
        'application/mxf' => ['mxf'],
        'application/n-quads' => ['nq'],
        'application/n-triples' => ['nt'],
        'application/nappdf' => ['pdf'],
        'application/node' => ['cjs'],
        'application/octet-stream' => ['bin', 'dms', 'lrf', 'mar', 'so', 'dist', 'distz', 'pkg', 'bpk', 'dump', 'elc', 'deploy', 'exe', 'dll', 'deb', 'dmg', 'iso', 'img', 'msi', 'msp', 'msm', 'buffer'],
        'application/oda' => ['oda'],
        'application/oebps-package+xml' => ['opf'],
        'application/ogg' => ['ogx'],
        'application/omdoc+xml' => ['omdoc'],
        'application/onenote' => ['onetoc', 'onetoc2', 'onetmp', 'onepkg'],
        'application/ovf' => ['ova'],
        'application/owl+xml' => ['owx'],
        'application/oxps' => ['oxps'],
        'application/p2p-overlay+xml' => ['relo'],
        'application/patch-ops-error+xml' => ['xer'],
        'application/pcap' => ['pcap', 'cap', 'dmp'],
        'application/pdf' => ['pdf'],
        'application/pgp' => ['pgp', 'gpg', 'asc'],
        'application/pgp-encrypted' => ['pgp', 'gpg', 'asc'],
        'application/pgp-keys' => ['asc', 'skr', 'pkr', 'pgp', 'gpg', 'key'],
        'application/pgp-signature' => ['sig', 'asc', 'pgp', 'gpg'],
        'application/photoshop' => ['psd'],
        'application/pics-rules' => ['prf'],
        'application/pkcs10' => ['p10'],
        'application/pkcs12' => ['p12', 'pfx'],
        'application/pkcs7-mime' => ['p7m', 'p7c'],
        'application/pkcs7-signature' => ['p7s'],
        'application/pkcs8' => ['p8'],
        'application/pkcs8-encrypted' => ['p8e'],
        'application/pkix-attr-cert' => ['ac'],
        'application/pkix-cert' => ['cer'],
        'application/pkix-crl' => ['crl'],
        'application/pkix-pkipath' => ['pkipath'],
        'application/pkixcmp' => ['pki'],
        'application/pls' => ['pls'],
        'application/pls+xml' => ['pls'],
        'application/postscript' => ['ai', 'eps', 'ps'],
        'application/powerpoint' => ['ppz', 'ppt', 'pps', 'pot'],
        'application/provenance+xml' => ['provx'],
        'application/prs.cww' => ['cww'],
        'application/prs.wavefront-obj' => ['obj'],
        'application/prs.xsf+xml' => ['xsf'],
        'application/pskc+xml' => ['pskcxml'],
        'application/ram' => ['ram'],
        'application/raml+yaml' => ['raml'],
        'application/rdf+xml' => ['rdf', 'owl', 'rdfs'],
        'application/reginfo+xml' => ['rif'],
        'application/relax-ng-compact-syntax' => ['rnc'],
        'application/resource-lists+xml' => ['rl'],
        'application/resource-lists-diff+xml' => ['rld'],
        'application/rls-services+xml' => ['rs'],
        'application/route-apd+xml' => ['rapd'],
        'application/route-s-tsid+xml' => ['sls'],
        'application/route-usd+xml' => ['rusd'],
        'application/rpki-ghostbusters' => ['gbr'],
        'application/rpki-manifest' => ['mft'],
        'application/rpki-roa' => ['roa'],
        'application/rsd+xml' => ['rsd'],
        'application/rss+xml' => ['rss'],
        'application/rtf' => ['rtf'],
        'application/sbml+xml' => ['sbml'],
        'application/schema+json' => ['json'],
        'application/scvp-cv-request' => ['scq'],
        'application/scvp-cv-response' => ['scs'],
        'application/scvp-vp-request' => ['spq'],
        'application/scvp-vp-response' => ['spp'],
        'application/sdp' => ['sdp'],
        'application/senml+xml' => ['senmlx'],
        'application/sensml+xml' => ['sensmlx'],
        'application/set-payment-initiation' => ['setpay'],
        'application/set-registration-initiation' => ['setreg'],
        'application/shf+xml' => ['shf'],
        'application/sieve' => ['siv', 'sieve'],
        'application/smil' => ['smil', 'smi', 'sml', 'kino'],
        'application/smil+xml' => ['smi', 'smil', 'sml', 'kino'],
        'application/sparql-query' => ['rq', 'qs'],
        'application/sparql-results+xml' => ['srx'],
        'application/sql' => ['sql'],
        'application/srgs' => ['gram'],
        'application/srgs+xml' => ['grxml'],
        'application/sru+xml' => ['sru'],
        'application/ssdl+xml' => ['ssdl'],
        'application/ssml+xml' => ['ssml'],
        'application/stuffit' => ['sit', 'hqx'],
        'application/swid+xml' => ['swidtag'],
        'application/tei+xml' => ['tei', 'teicorpus'],
        'application/tga' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'application/thraud+xml' => ['tfi'],
        'application/timestamped-data' => ['tsd'],
        'application/toml' => ['toml'],
        'application/trig' => ['trig'],
        'application/ttml+xml' => ['ttml'],
        'application/ubjson' => ['ubj'],
        'application/urc-ressheet+xml' => ['rsheet'],
        'application/urc-targetdesc+xml' => ['td'],
        'application/vnd.1000minds.decision-model+xml' => ['1km'],
        'application/vnd.3gpp.pic-bw-large' => ['plb'],
        'application/vnd.3gpp.pic-bw-small' => ['psb'],
        'application/vnd.3gpp.pic-bw-var' => ['pvb'],
        'application/vnd.3gpp2.tcap' => ['tcap'],
        'application/vnd.3m.post-it-notes' => ['pwn'],
        'application/vnd.accpac.simply.aso' => ['aso'],
        'application/vnd.accpac.simply.imp' => ['imp'],
        'application/vnd.acucobol' => ['acu'],
        'application/vnd.acucorp' => ['atc', 'acutc'],
        'application/vnd.adobe.air-application-installer-package+zip' => ['air'],
        'application/vnd.adobe.flash.movie' => ['swf', 'spl'],
        'application/vnd.adobe.formscentral.fcdt' => ['fcdt'],
        'application/vnd.adobe.fxp' => ['fxp', 'fxpl'],
        'application/vnd.adobe.illustrator' => ['ai'],
        'application/vnd.adobe.xdp+xml' => ['xdp'],
        'application/vnd.adobe.xfdf' => ['xfdf'],
        'application/vnd.age' => ['age'],
        'application/vnd.ahead.space' => ['ahead'],
        'application/vnd.airzip.filesecure.azf' => ['azf'],
        'application/vnd.airzip.filesecure.azs' => ['azs'],
        'application/vnd.amazon.ebook' => ['azw'],
        'application/vnd.amazon.mobi8-ebook' => ['azw3', 'kfx'],
        'application/vnd.americandynamics.acc' => ['acc'],
        'application/vnd.amiga.ami' => ['ami'],
        'application/vnd.android.package-archive' => ['apk'],
        'application/vnd.anser-web-certificate-issue-initiation' => ['cii'],
        'application/vnd.anser-web-funds-transfer-initiation' => ['fti'],
        'application/vnd.antix.game-component' => ['atx'],
        'application/vnd.apache.parquet' => ['parquet'],
        'application/vnd.appimage' => ['appimage'],
        'application/vnd.apple.installer+xml' => ['mpkg'],
        'application/vnd.apple.keynote' => ['key', 'keynote'],
        'application/vnd.apple.mpegurl' => ['m3u8', 'm3u'],
        'application/vnd.apple.numbers' => ['numbers'],
        'application/vnd.apple.pages' => ['pages'],
        'application/vnd.apple.pkpass' => ['pkpass'],
        'application/vnd.aristanetworks.swi' => ['swi'],
        'application/vnd.astraea-software.iota' => ['iota'],
        'application/vnd.audiograph' => ['aep'],
        'application/vnd.balsamiq.bmml+xml' => ['bmml'],
        'application/vnd.blueice.multipass' => ['mpm'],
        'application/vnd.bmi' => ['bmi'],
        'application/vnd.businessobjects' => ['rep'],
        'application/vnd.chemdraw+xml' => ['cdxml'],
        'application/vnd.chess-pgn' => ['pgn'],
        'application/vnd.chipnuts.karaoke-mmd' => ['mmd'],
        'application/vnd.cinderella' => ['cdy'],
        'application/vnd.citationstyles.style+xml' => ['csl'],
        'application/vnd.claymore' => ['cla'],
        'application/vnd.cloanto.rp9' => ['rp9'],
        'application/vnd.clonk.c4group' => ['c4g', 'c4d', 'c4f', 'c4p', 'c4u'],
        'application/vnd.cluetrust.cartomobile-config' => ['c11amc'],
        'application/vnd.cluetrust.cartomobile-config-pkg' => ['c11amz'],
        'application/vnd.coffeescript' => ['coffee'],
        'application/vnd.comicbook+zip' => ['cbz'],
        'application/vnd.comicbook-rar' => ['cbr'],
        'application/vnd.commonspace' => ['csp'],
        'application/vnd.contact.cmsg' => ['cdbcmsg'],
        'application/vnd.corel-draw' => ['cdr'],
        'application/vnd.cosmocaller' => ['cmc'],
        'application/vnd.crick.clicker' => ['clkx'],
        'application/vnd.crick.clicker.keyboard' => ['clkk'],
        'application/vnd.crick.clicker.palette' => ['clkp'],
        'application/vnd.crick.clicker.template' => ['clkt'],
        'application/vnd.crick.clicker.wordbank' => ['clkw'],
        'application/vnd.criticaltools.wbs+xml' => ['wbs'],
        'application/vnd.ctc-posml' => ['pml'],
        'application/vnd.cups-ppd' => ['ppd'],
        'application/vnd.curl.car' => ['car'],
        'application/vnd.curl.pcurl' => ['pcurl'],
        'application/vnd.dart' => ['dart'],
        'application/vnd.data-vision.rdz' => ['rdz'],
        'application/vnd.dbf' => ['dbf'],
        'application/vnd.debian.binary-package' => ['deb', 'udeb'],
        'application/vnd.dece.data' => ['uvf', 'uvvf', 'uvd', 'uvvd'],
        'application/vnd.dece.ttml+xml' => ['uvt', 'uvvt'],
        'application/vnd.dece.unspecified' => ['uvx', 'uvvx'],
        'application/vnd.dece.zip' => ['uvz', 'uvvz'],
        'application/vnd.denovo.fcselayout-link' => ['fe_launch'],
        'application/vnd.dna' => ['dna'],
        'application/vnd.dolby.mlp' => ['mlp'],
        'application/vnd.dpgraph' => ['dpg'],
        'application/vnd.dreamfactory' => ['dfac'],
        'application/vnd.ds-keypoint' => ['kpxx'],
        'application/vnd.dvb.ait' => ['ait'],
        'application/vnd.dvb.service' => ['svc'],
        'application/vnd.dynageo' => ['geo'],
        'application/vnd.ecowin.chart' => ['mag'],
        'application/vnd.efi.img' => ['raw-disk-image', 'img'],
        'application/vnd.efi.iso' => ['iso', 'iso9660'],
        'application/vnd.emusic-emusic_package' => ['emp'],
        'application/vnd.enliven' => ['nml'],
        'application/vnd.epson.esf' => ['esf'],
        'application/vnd.epson.msf' => ['msf'],
        'application/vnd.epson.quickanime' => ['qam'],
        'application/vnd.epson.salt' => ['slt'],
        'application/vnd.epson.ssf' => ['ssf'],
        'application/vnd.eszigno3+xml' => ['es3', 'et3'],
        'application/vnd.etsi.asic-e+zip' => ['asice'],
        'application/vnd.ezpix-album' => ['ez2'],
        'application/vnd.ezpix-package' => ['ez3'],
        'application/vnd.fdf' => ['fdf'],
        'application/vnd.fdsn.mseed' => ['mseed'],
        'application/vnd.fdsn.seed' => ['seed', 'dataless'],
        'application/vnd.flatpak' => ['flatpak', 'xdgapp'],
        'application/vnd.flatpak.ref' => ['flatpakref'],
        'application/vnd.flatpak.repo' => ['flatpakrepo'],
        'application/vnd.flographit' => ['gph'],
        'application/vnd.fluxtime.clip' => ['ftc'],
        'application/vnd.framemaker' => ['fm', 'frame', 'maker', 'book'],
        'application/vnd.frogans.fnc' => ['fnc'],
        'application/vnd.frogans.ltf' => ['ltf'],
        'application/vnd.fsc.weblaunch' => ['fsc'],
        'application/vnd.fujitsu.oasys' => ['oas'],
        'application/vnd.fujitsu.oasys2' => ['oa2'],
        'application/vnd.fujitsu.oasys3' => ['oa3'],
        'application/vnd.fujitsu.oasysgp' => ['fg5'],
        'application/vnd.fujitsu.oasysprs' => ['bh2'],
        'application/vnd.fujixerox.ddd' => ['ddd'],
        'application/vnd.fujixerox.docuworks' => ['xdw'],
        'application/vnd.fujixerox.docuworks.binder' => ['xbd'],
        'application/vnd.fuzzysheet' => ['fzs'],
        'application/vnd.genomatix.tuxedo' => ['txd'],
        'application/vnd.geo+json' => ['geojson', 'geo.json'],
        'application/vnd.geogebra.file' => ['ggb'],
        'application/vnd.geogebra.slides' => ['ggs'],
        'application/vnd.geogebra.tool' => ['ggt'],
        'application/vnd.geometry-explorer' => ['gex', 'gre'],
        'application/vnd.geonext' => ['gxt'],
        'application/vnd.geoplan' => ['g2w'],
        'application/vnd.geospace' => ['g3w'],
        'application/vnd.gerber' => ['gbr'],
        'application/vnd.gmx' => ['gmx'],
        'application/vnd.google-apps.document' => ['gdoc'],
        'application/vnd.google-apps.presentation' => ['gslides'],
        'application/vnd.google-apps.spreadsheet' => ['gsheet'],
        'application/vnd.google-earth.kml+xml' => ['kml'],
        'application/vnd.google-earth.kmz' => ['kmz'],
        'application/vnd.gov.sk.xmldatacontainer+xml' => ['xdcf'],
        'application/vnd.grafeq' => ['gqf', 'gqs'],
        'application/vnd.groove-account' => ['gac'],
        'application/vnd.groove-help' => ['ghf'],
        'application/vnd.groove-identity-message' => ['gim'],
        'application/vnd.groove-injector' => ['grv'],
        'application/vnd.groove-tool-message' => ['gtm'],
        'application/vnd.groove-tool-template' => ['tpl'],
        'application/vnd.groove-vcard' => ['vcg'],
        'application/vnd.haansoft-hwp' => ['hwp'],
        'application/vnd.haansoft-hwt' => ['hwt'],
        'application/vnd.hal+xml' => ['hal'],
        'application/vnd.handheld-entertainment+xml' => ['zmm'],
        'application/vnd.hbci' => ['hbci'],
        'application/vnd.hhe.lesson-player' => ['les'],
        'application/vnd.hp-hpgl' => ['hpgl'],
        'application/vnd.hp-hpid' => ['hpid'],
        'application/vnd.hp-hps' => ['hps'],
        'application/vnd.hp-jlyt' => ['jlt'],
        'application/vnd.hp-pcl' => ['pcl'],
        'application/vnd.hp-pclxl' => ['pclxl'],
        'application/vnd.hydrostatix.sof-data' => ['sfd-hdstx'],
        'application/vnd.ibm.minipay' => ['mpy'],
        'application/vnd.ibm.modcap' => ['afp', 'listafp', 'list3820'],
        'application/vnd.ibm.rights-management' => ['irm'],
        'application/vnd.ibm.secure-container' => ['sc'],
        'application/vnd.iccprofile' => ['icc', 'icm'],
        'application/vnd.igloader' => ['igl'],
        'application/vnd.immervision-ivp' => ['ivp'],
        'application/vnd.immervision-ivu' => ['ivu'],
        'application/vnd.insors.igm' => ['igm'],
        'application/vnd.intercon.formnet' => ['xpw', 'xpx'],
        'application/vnd.intergeo' => ['i2g'],
        'application/vnd.intu.qbo' => ['qbo'],
        'application/vnd.intu.qfx' => ['qfx'],
        'application/vnd.ipunplugged.rcprofile' => ['rcprofile'],
        'application/vnd.irepository.package+xml' => ['irp'],
        'application/vnd.is-xpr' => ['xpr'],
        'application/vnd.isac.fcs' => ['fcs'],
        'application/vnd.jam' => ['jam'],
        'application/vnd.jcp.javame.midlet-rms' => ['rms'],
        'application/vnd.jisp' => ['jisp'],
        'application/vnd.joost.joda-archive' => ['joda'],
        'application/vnd.kahootz' => ['ktz', 'ktr'],
        'application/vnd.kde.karbon' => ['karbon'],
        'application/vnd.kde.kchart' => ['chrt'],
        'application/vnd.kde.kformula' => ['kfo'],
        'application/vnd.kde.kivio' => ['flw'],
        'application/vnd.kde.kontour' => ['kon'],
        'application/vnd.kde.kpresenter' => ['kpr', 'kpt'],
        'application/vnd.kde.kspread' => ['ksp'],
        'application/vnd.kde.kword' => ['kwd', 'kwt'],
        'application/vnd.kenameaapp' => ['htke'],
        'application/vnd.kidspiration' => ['kia'],
        'application/vnd.kinar' => ['kne', 'knp'],
        'application/vnd.koan' => ['skp', 'skd', 'skt', 'skm'],
        'application/vnd.kodak-descriptor' => ['sse'],
        'application/vnd.las.las+xml' => ['lasxml'],
        'application/vnd.llamagraphics.life-balance.desktop' => ['lbd'],
        'application/vnd.llamagraphics.life-balance.exchange+xml' => ['lbe'],
        'application/vnd.lotus-1-2-3' => ['123', 'wk1', 'wk3', 'wk4', 'wks'],
        'application/vnd.lotus-approach' => ['apr'],
        'application/vnd.lotus-freelance' => ['pre'],
        'application/vnd.lotus-notes' => ['nsf'],
        'application/vnd.lotus-organizer' => ['org'],
        'application/vnd.lotus-screencam' => ['scm'],
        'application/vnd.lotus-wordpro' => ['lwp'],
        'application/vnd.macports.portpkg' => ['portpkg'],
        'application/vnd.mapbox-vector-tile' => ['mvt'],
        'application/vnd.mcd' => ['mcd'],
        'application/vnd.medcalcdata' => ['mc1'],
        'application/vnd.mediastation.cdkey' => ['cdkey'],
        'application/vnd.mfer' => ['mwf'],
        'application/vnd.mfmp' => ['mfm'],
        'application/vnd.micrografx.flo' => ['flo'],
        'application/vnd.micrografx.igx' => ['igx'],
        'application/vnd.microsoft.portable-executable' => ['exe', 'dll', 'cpl', 'drv', 'scr', 'efi', 'ocx', 'sys', 'lib'],
        'application/vnd.mif' => ['mif'],
        'application/vnd.mobius.daf' => ['daf'],
        'application/vnd.mobius.dis' => ['dis'],
        'application/vnd.mobius.mbk' => ['mbk'],
        'application/vnd.mobius.mqy' => ['mqy'],
        'application/vnd.mobius.msl' => ['msl'],
        'application/vnd.mobius.plc' => ['plc'],
        'application/vnd.mobius.txf' => ['txf'],
        'application/vnd.mophun.application' => ['mpn'],
        'application/vnd.mophun.certificate' => ['mpc'],
        'application/vnd.mozilla.xul+xml' => ['xul'],
        'application/vnd.ms-3mfdocument' => ['3mf'],
        'application/vnd.ms-access' => ['mdb'],
        'application/vnd.ms-artgalry' => ['cil'],
        'application/vnd.ms-asf' => ['asf'],
        'application/vnd.ms-cab-compressed' => ['cab'],
        'application/vnd.ms-excel' => ['xls', 'xlm', 'xla', 'xlc', 'xlt', 'xlw', 'xll', 'xld'],
        'application/vnd.ms-excel.addin.macroenabled.12' => ['xlam'],
        'application/vnd.ms-excel.sheet.binary.macroenabled.12' => ['xlsb'],
        'application/vnd.ms-excel.sheet.macroenabled.12' => ['xlsm'],
        'application/vnd.ms-excel.template.macroenabled.12' => ['xltm'],
        'application/vnd.ms-fontobject' => ['eot'],
        'application/vnd.ms-htmlhelp' => ['chm'],
        'application/vnd.ms-ims' => ['ims'],
        'application/vnd.ms-lrm' => ['lrm'],
        'application/vnd.ms-officetheme' => ['thmx'],
        'application/vnd.ms-outlook' => ['msg'],
        'application/vnd.ms-pki.seccat' => ['cat'],
        'application/vnd.ms-pki.stl' => ['stl'],
        'application/vnd.ms-powerpoint' => ['ppt', 'pps', 'pot', 'ppz'],
        'application/vnd.ms-powerpoint.addin.macroenabled.12' => ['ppam'],
        'application/vnd.ms-powerpoint.presentation.macroenabled.12' => ['pptm'],
        'application/vnd.ms-powerpoint.slide.macroenabled.12' => ['sldm'],
        'application/vnd.ms-powerpoint.slideshow.macroenabled.12' => ['ppsm'],
        'application/vnd.ms-powerpoint.template.macroenabled.12' => ['potm'],
        'application/vnd.ms-project' => ['mpp', 'mpt'],
        'application/vnd.ms-publisher' => ['pub'],
        'application/vnd.ms-tnef' => ['tnef', 'tnf'],
        'application/vnd.ms-visio.drawing.macroenabled.main+xml' => ['vsdm'],
        'application/vnd.ms-visio.drawing.main+xml' => ['vsdx'],
        'application/vnd.ms-visio.stencil.macroenabled.main+xml' => ['vssm'],
        'application/vnd.ms-visio.stencil.main+xml' => ['vssx'],
        'application/vnd.ms-visio.template.macroenabled.main+xml' => ['vstm'],
        'application/vnd.ms-visio.template.main+xml' => ['vstx'],
        'application/vnd.ms-word' => ['doc'],
        'application/vnd.ms-word.document.macroenabled.12' => ['docm'],
        'application/vnd.ms-word.template.macroenabled.12' => ['dotm'],
        'application/vnd.ms-works' => ['wps', 'wks', 'wcm', 'wdb', 'xlr'],
        'application/vnd.ms-wpl' => ['wpl'],
        'application/vnd.ms-xpsdocument' => ['xps'],
        'application/vnd.msaccess' => ['mdb'],
        'application/vnd.mseq' => ['mseq'],
        'application/vnd.musician' => ['mus'],
        'application/vnd.muvee.style' => ['msty'],
        'application/vnd.mynfc' => ['taglet'],
        'application/vnd.nato.bindingdataobject+xml' => ['bdo'],
        'application/vnd.neurolanguage.nlu' => ['nlu'],
        'application/vnd.nintendo.snes.rom' => ['sfc', 'smc'],
        'application/vnd.nitf' => ['ntf', 'nitf'],
        'application/vnd.noblenet-directory' => ['nnd'],
        'application/vnd.noblenet-sealer' => ['nns'],
        'application/vnd.noblenet-web' => ['nnw'],
        'application/vnd.nokia.n-gage.ac+xml' => ['ac'],
        'application/vnd.nokia.n-gage.data' => ['ngdat'],
        'application/vnd.nokia.n-gage.symbian.install' => ['n-gage'],
        'application/vnd.nokia.radio-preset' => ['rpst'],
        'application/vnd.nokia.radio-presets' => ['rpss'],
        'application/vnd.novadigm.edm' => ['edm'],
        'application/vnd.novadigm.edx' => ['edx'],
        'application/vnd.novadigm.ext' => ['ext'],
        'application/vnd.oasis.docbook+xml' => ['dbk', 'docbook'],
        'application/vnd.oasis.opendocument.base' => ['odb'],
        'application/vnd.oasis.opendocument.chart' => ['odc'],
        'application/vnd.oasis.opendocument.chart-template' => ['otc'],
        'application/vnd.oasis.opendocument.database' => ['odb'],
        'application/vnd.oasis.opendocument.formula' => ['odf'],
        'application/vnd.oasis.opendocument.formula-template' => ['odft', 'otf'],
        'application/vnd.oasis.opendocument.graphics' => ['odg'],
        'application/vnd.oasis.opendocument.graphics-flat-xml' => ['fodg'],
        'application/vnd.oasis.opendocument.graphics-template' => ['otg'],
        'application/vnd.oasis.opendocument.image' => ['odi'],
        'application/vnd.oasis.opendocument.image-template' => ['oti'],
        'application/vnd.oasis.opendocument.presentation' => ['odp'],
        'application/vnd.oasis.opendocument.presentation-flat-xml' => ['fodp'],
        'application/vnd.oasis.opendocument.presentation-template' => ['otp'],
        'application/vnd.oasis.opendocument.spreadsheet' => ['ods'],
        'application/vnd.oasis.opendocument.spreadsheet-flat-xml' => ['fods'],
        'application/vnd.oasis.opendocument.spreadsheet-template' => ['ots'],
        'application/vnd.oasis.opendocument.text' => ['odt'],
        'application/vnd.oasis.opendocument.text-flat-xml' => ['fodt'],
        'application/vnd.oasis.opendocument.text-master' => ['odm'],
        'application/vnd.oasis.opendocument.text-master-template' => ['otm'],
        'application/vnd.oasis.opendocument.text-template' => ['ott'],
        'application/vnd.oasis.opendocument.text-web' => ['oth'],
        'application/vnd.olpc-sugar' => ['xo'],
        'application/vnd.oma.dd2+xml' => ['dd2'],
        'application/vnd.openblox.game+xml' => ['obgx'],
        'application/vnd.openofficeorg.extension' => ['oxt'],
        'application/vnd.openstreetmap.data+xml' => ['osm'],
        'application/vnd.openxmlformats-officedocument.presentationml.presentation' => ['pptx'],
        'application/vnd.openxmlformats-officedocument.presentationml.slide' => ['sldx'],
        'application/vnd.openxmlformats-officedocument.presentationml.slideshow' => ['ppsx'],
        'application/vnd.openxmlformats-officedocument.presentationml.template' => ['potx'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => ['xlsx'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.template' => ['xltx'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => ['docx'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.template' => ['dotx'],
        'application/vnd.osgeo.mapguide.package' => ['mgp'],
        'application/vnd.osgi.dp' => ['dp'],
        'application/vnd.osgi.subsystem' => ['esa'],
        'application/vnd.palm' => ['pdb', 'pqa', 'oprc', 'prc'],
        'application/vnd.pawaafile' => ['paw'],
        'application/vnd.pg.format' => ['str'],
        'application/vnd.pg.osasli' => ['ei6'],
        'application/vnd.picsel' => ['efif'],
        'application/vnd.pmi.widget' => ['wg'],
        'application/vnd.pocketlearn' => ['plf'],
        'application/vnd.powerbuilder6' => ['pbd'],
        'application/vnd.previewsystems.box' => ['box'],
        'application/vnd.proteus.magazine' => ['mgz'],
        'application/vnd.publishare-delta-tree' => ['qps'],
        'application/vnd.pvi.ptid1' => ['ptid'],
        'application/vnd.pwg-xhtml-print+xml' => ['xhtm'],
        'application/vnd.quark.quarkxpress' => ['qxd', 'qxt', 'qwd', 'qwt', 'qxl', 'qxb', 'qxp'],
        'application/vnd.rar' => ['rar'],
        'application/vnd.realvnc.bed' => ['bed'],
        'application/vnd.recordare.musicxml' => ['mxl'],
        'application/vnd.recordare.musicxml+xml' => ['musicxml'],
        'application/vnd.rig.cryptonote' => ['cryptonote'],
        'application/vnd.rim.cod' => ['cod'],
        'application/vnd.rn-realmedia' => ['rm', 'rmj', 'rmm', 'rms', 'rmx', 'rmvb'],
        'application/vnd.rn-realmedia-vbr' => ['rmvb', 'rm', 'rmj', 'rmm', 'rms', 'rmx'],
        'application/vnd.route66.link66+xml' => ['link66'],
        'application/vnd.sailingtracker.track' => ['st'],
        'application/vnd.sdp' => ['sdp'],
        'application/vnd.seemail' => ['see'],
        'application/vnd.sema' => ['sema'],
        'application/vnd.semd' => ['semd'],
        'application/vnd.semf' => ['semf'],
        'application/vnd.shana.informed.formdata' => ['ifm'],
        'application/vnd.shana.informed.formtemplate' => ['itp'],
        'application/vnd.shana.informed.interchange' => ['iif'],
        'application/vnd.shana.informed.package' => ['ipk'],
        'application/vnd.simtech-mindmapper' => ['twd', 'twds'],
        'application/vnd.smaf' => ['mmf', 'smaf'],
        'application/vnd.smart.teacher' => ['teacher'],
        'application/vnd.snap' => ['snap'],
        'application/vnd.software602.filler.form+xml' => ['fo'],
        'application/vnd.solent.sdkm+xml' => ['sdkm', 'sdkd'],
        'application/vnd.spotfire.dxp' => ['dxp'],
        'application/vnd.spotfire.sfs' => ['sfs'],
        'application/vnd.sqlite3' => ['sqlite3'],
        'application/vnd.squashfs' => ['sfs', 'sqfs', 'sqsh', 'squashfs'],
        'application/vnd.stardivision.calc' => ['sdc'],
        'application/vnd.stardivision.chart' => ['sds'],
        'application/vnd.stardivision.draw' => ['sda'],
        'application/vnd.stardivision.impress' => ['sdd'],
        'application/vnd.stardivision.impress-packed' => ['sdp'],
        'application/vnd.stardivision.mail' => ['sdm'],
        'application/vnd.stardivision.math' => ['smf'],
        'application/vnd.stardivision.writer' => ['sdw', 'vor'],
        'application/vnd.stardivision.writer-global' => ['sgl'],
        'application/vnd.stepmania.package' => ['smzip'],
        'application/vnd.stepmania.stepchart' => ['sm'],
        'application/vnd.sun.wadl+xml' => ['wadl'],
        'application/vnd.sun.xml.base' => ['odb'],
        'application/vnd.sun.xml.calc' => ['sxc'],
        'application/vnd.sun.xml.calc.template' => ['stc'],
        'application/vnd.sun.xml.draw' => ['sxd'],
        'application/vnd.sun.xml.draw.template' => ['std'],
        'application/vnd.sun.xml.impress' => ['sxi'],
        'application/vnd.sun.xml.impress.template' => ['sti'],
        'application/vnd.sun.xml.math' => ['sxm'],
        'application/vnd.sun.xml.writer' => ['sxw'],
        'application/vnd.sun.xml.writer.global' => ['sxg'],
        'application/vnd.sun.xml.writer.template' => ['stw'],
        'application/vnd.sus-calendar' => ['sus', 'susp'],
        'application/vnd.svd' => ['svd'],
        'application/vnd.symbian.install' => ['sis', 'sisx'],
        'application/vnd.syncml+xml' => ['xsm'],
        'application/vnd.syncml.dm+wbxml' => ['bdm'],
        'application/vnd.syncml.dm+xml' => ['xdm'],
        'application/vnd.syncml.dmddf+xml' => ['ddf'],
        'application/vnd.tao.intent-module-archive' => ['tao'],
        'application/vnd.tcpdump.pcap' => ['pcap', 'cap', 'dmp'],
        'application/vnd.tmobile-livetv' => ['tmo'],
        'application/vnd.trid.tpt' => ['tpt'],
        'application/vnd.triscape.mxs' => ['mxs'],
        'application/vnd.trueapp' => ['tra'],
        'application/vnd.truedoc' => ['pfr'],
        'application/vnd.ufdl' => ['ufd', 'ufdl'],
        'application/vnd.uiq.theme' => ['utz'],
        'application/vnd.umajin' => ['umj'],
        'application/vnd.unity' => ['unityweb'],
        'application/vnd.uoml+xml' => ['uoml', 'uo'],
        'application/vnd.vcx' => ['vcx'],
        'application/vnd.visio' => ['vsd', 'vst', 'vss', 'vsw'],
        'application/vnd.visionary' => ['vis'],
        'application/vnd.vsf' => ['vsf'],
        'application/vnd.wap.wbxml' => ['wbxml'],
        'application/vnd.wap.wmlc' => ['wmlc'],
        'application/vnd.wap.wmlscriptc' => ['wmlsc'],
        'application/vnd.webturbo' => ['wtb'],
        'application/vnd.wolfram.player' => ['nbp'],
        'application/vnd.wordperfect' => ['wpd', 'wp', 'wp4', 'wp5', 'wp6', 'wpp'],
        'application/vnd.wqd' => ['wqd'],
        'application/vnd.wt.stf' => ['stf'],
        'application/vnd.xara' => ['xar'],
        'application/vnd.xdgapp' => ['flatpak', 'xdgapp'],
        'application/vnd.xfdl' => ['xfdl'],
        'application/vnd.yamaha.hv-dic' => ['hvd'],
        'application/vnd.yamaha.hv-script' => ['hvs'],
        'application/vnd.yamaha.hv-voice' => ['hvp'],
        'application/vnd.yamaha.openscoreformat' => ['osf'],
        'application/vnd.yamaha.openscoreformat.osfpvg+xml' => ['osfpvg'],
        'application/vnd.yamaha.smaf-audio' => ['saf'],
        'application/vnd.yamaha.smaf-phrase' => ['spf'],
        'application/vnd.yellowriver-custom-menu' => ['cmp'],
        'application/vnd.youtube.yt' => ['yt'],
        'application/vnd.zul' => ['zir', 'zirz'],
        'application/vnd.zzazz.deck+xml' => ['zaz'],
        'application/voicexml+xml' => ['vxml'],
        'application/wasm' => ['wasm'],
        'application/watcherinfo+xml' => ['wif'],
        'application/widget' => ['wgt'],
        'application/winhlp' => ['hlp'],
        'application/wk1' => ['123', 'wk1', 'wk3', 'wk4', 'wks'],
        'application/wmf' => ['wmf'],
        'application/wordperfect' => ['wp', 'wp4', 'wp5', 'wp6', 'wpd', 'wpp'],
        'application/wsdl+xml' => ['wsdl'],
        'application/wspolicy+xml' => ['wspolicy'],
        'application/wwf' => ['wwf'],
        'application/x-123' => ['123', 'wk1', 'wk3', 'wk4', 'wks'],
        'application/x-7z-compressed' => ['7z', '7z.001'],
        'application/x-abiword' => ['abw', 'abw.CRASHED', 'abw.gz', 'zabw'],
        'application/x-ace' => ['ace'],
        'application/x-ace-compressed' => ['ace'],
        'application/x-alz' => ['alz'],
        'application/x-amiga-disk-format' => ['adf'],
        'application/x-amipro' => ['sam'],
        'application/x-annodex' => ['anx'],
        'application/x-aportisdoc' => ['pdb', 'pdc'],
        'application/x-apple-diskimage' => ['dmg'],
        'application/x-apple-systemprofiler+xml' => ['spx'],
        'application/x-appleworks-document' => ['cwk'],
        'application/x-applix-spreadsheet' => ['as'],
        'application/x-applix-word' => ['aw'],
        'application/x-archive' => ['a', 'ar', 'lib'],
        'application/x-arj' => ['arj'],
        'application/x-asar' => ['asar'],
        'application/x-asp' => ['asp'],
        'application/x-atari-2600-rom' => ['a26'],
        'application/x-atari-7800-rom' => ['a78'],
        'application/x-atari-lynx-rom' => ['lnx'],
        'application/x-authorware-bin' => ['aab', 'x32', 'u32', 'vox'],
        'application/x-authorware-map' => ['aam'],
        'application/x-authorware-seg' => ['aas'],
        'application/x-awk' => ['awk'],
        'application/x-bat' => ['bat'],
        'application/x-bcpio' => ['bcpio'],
        'application/x-bdoc' => ['bdoc'],
        'application/x-bittorrent' => ['torrent'],
        'application/x-blender' => ['blend', 'blender'],
        'application/x-blorb' => ['blb', 'blorb'],
        'application/x-bps-patch' => ['bps'],
        'application/x-bsdiff' => ['bsdiff'],
        'application/x-bz2' => ['bz2'],
        'application/x-bzdvi' => ['dvi.bz2'],
        'application/x-bzip' => ['bz', 'bz2'],
        'application/x-bzip-compressed-tar' => ['tar.bz2', 'tbz2', 'tb2'],
        'application/x-bzip1' => ['bz'],
        'application/x-bzip1-compressed-tar' => ['tar.bz', 'tbz'],
        'application/x-bzip2' => ['bz2', 'boz'],
        'application/x-bzip2-compressed-tar' => ['tar.bz2', 'tbz2', 'tb2'],
        'application/x-bzip3' => ['bz3'],
        'application/x-bzip3-compressed-tar' => ['tar.bz3', 'tbz3'],
        'application/x-bzpdf' => ['pdf.bz2'],
        'application/x-bzpostscript' => ['ps.bz2'],
        'application/x-cb7' => ['cb7'],
        'application/x-cbr' => ['cbr', 'cba', 'cbt', 'cbz', 'cb7'],
        'application/x-cbt' => ['cbt'],
        'application/x-cbz' => ['cbz'],
        'application/x-ccmx' => ['ccmx'],
        'application/x-cd-image' => ['iso', 'iso9660'],
        'application/x-cdlink' => ['vcd'],
        'application/x-cdr' => ['cdr'],
        'application/x-cdrdao-toc' => ['toc'],
        'application/x-cfs-compressed' => ['cfs'],
        'application/x-chat' => ['chat'],
        'application/x-chess-pgn' => ['pgn'],
        'application/x-chm' => ['chm'],
        'application/x-chrome-extension' => ['crx'],
        'application/x-cisco-vpn-settings' => ['pcf'],
        'application/x-cocoa' => ['cco'],
        'application/x-compress' => ['Z'],
        'application/x-compressed-iso' => ['cso'],
        'application/x-compressed-tar' => ['tar.gz', 'tgz'],
        'application/x-conference' => ['nsc'],
        'application/x-coreldraw' => ['cdr'],
        'application/x-cpio' => ['cpio'],
        'application/x-cpio-compressed' => ['cpio.gz'],
        'application/x-csh' => ['csh'],
        'application/x-cue' => ['cue'],
        'application/x-dar' => ['dar'],
        'application/x-dbase' => ['dbf'],
        'application/x-dbf' => ['dbf'],
        'application/x-dc-rom' => ['dc'],
        'application/x-deb' => ['deb', 'udeb'],
        'application/x-debian-package' => ['deb', 'udeb'],
        'application/x-designer' => ['ui'],
        'application/x-desktop' => ['desktop', 'kdelnk'],
        'application/x-dgc-compressed' => ['dgc'],
        'application/x-dia-diagram' => ['dia'],
        'application/x-dia-shape' => ['shape'],
        'application/x-director' => ['dir', 'dcr', 'dxr', 'cst', 'cct', 'cxt', 'w3d', 'fgd', 'swa'],
        'application/x-discjuggler-cd-image' => ['cdi'],
        'application/x-docbook+xml' => ['dbk', 'docbook'],
        'application/x-doom' => ['wad'],
        'application/x-doom-wad' => ['wad'],
        'application/x-dosexec' => ['exe'],
        'application/x-dreamcast-rom' => ['iso'],
        'application/x-dtbncx+xml' => ['ncx'],
        'application/x-dtbook+xml' => ['dtb'],
        'application/x-dtbresource+xml' => ['res'],
        'application/x-dvi' => ['dvi'],
        'application/x-e-theme' => ['etheme'],
        'application/x-egon' => ['egon'],
        'application/x-emf' => ['emf'],
        'application/x-envoy' => ['evy'],
        'application/x-eris-link+cbor' => ['eris'],
        'application/x-eva' => ['eva'],
        'application/x-excellon' => ['drl'],
        'application/x-fd-file' => ['fd', 'qd'],
        'application/x-fds-disk' => ['fds'],
        'application/x-fictionbook' => ['fb2'],
        'application/x-fictionbook+xml' => ['fb2'],
        'application/x-fishscript' => ['fish'],
        'application/x-flash-video' => ['flv'],
        'application/x-fluid' => ['fl'],
        'application/x-font-afm' => ['afm'],
        'application/x-font-bdf' => ['bdf'],
        'application/x-font-ghostscript' => ['gsf'],
        'application/x-font-linux-psf' => ['psf'],
        'application/x-font-otf' => ['otf'],
        'application/x-font-pcf' => ['pcf', 'pcf.Z', 'pcf.gz'],
        'application/x-font-snf' => ['snf'],
        'application/x-font-speedo' => ['spd'],
        'application/x-font-truetype' => ['ttf'],
        'application/x-font-ttf' => ['ttf'],
        'application/x-font-ttx' => ['ttx'],
        'application/x-font-type1' => ['pfa', 'pfb', 'pfm', 'afm', 'gsf'],
        'application/x-font-woff' => ['woff'],
        'application/x-frame' => ['fm'],
        'application/x-freearc' => ['arc'],
        'application/x-futuresplash' => ['spl'],
        'application/x-gameboy-color-rom' => ['gbc', 'cgb'],
        'application/x-gameboy-rom' => ['gb', 'sgb'],
        'application/x-gamecube-iso-image' => ['iso'],
        'application/x-gamecube-rom' => ['iso'],
        'application/x-gamegear-rom' => ['gg'],
        'application/x-gba-rom' => ['gba', 'agb'],
        'application/x-gca-compressed' => ['gca'],
        'application/x-gd-rom-cue' => ['gdi'],
        'application/x-gdscript' => ['gd'],
        'application/x-gedcom' => ['ged', 'gedcom'],
        'application/x-genesis-32x-rom' => ['32x', 'mdx'],
        'application/x-genesis-rom' => ['gen', 'smd', 'md', 'sgd'],
        'application/x-gerber' => ['gbr'],
        'application/x-gerber-job' => ['gbrjob'],
        'application/x-gettext' => ['po'],
        'application/x-gettext-translation' => ['gmo', 'mo'],
        'application/x-glade' => ['glade'],
        'application/x-glulx' => ['ulx'],
        'application/x-gnome-app-info' => ['desktop', 'kdelnk'],
        'application/x-gnucash' => ['gnucash', 'gnc', 'xac'],
        'application/x-gnumeric' => ['gnumeric'],
        'application/x-gnuplot' => ['gp', 'gplt', 'gnuplot'],
        'application/x-go-sgf' => ['sgf'],
        'application/x-godot-resource' => ['res', 'tres'],
        'application/x-godot-scene' => ['scn', 'tscn', 'escn'],
        'application/x-godot-shader' => ['gdshader'],
        'application/x-gpx' => ['gpx'],
        'application/x-gpx+xml' => ['gpx'],
        'application/x-gramps-xml' => ['gramps'],
        'application/x-graphite' => ['gra'],
        'application/x-gtar' => ['gtar', 'tar', 'gem'],
        'application/x-gtk-builder' => ['ui'],
        'application/x-gz-font-linux-psf' => ['psf.gz'],
        'application/x-gzdvi' => ['dvi.gz'],
        'application/x-gzip' => ['gz'],
        'application/x-gzpdf' => ['pdf.gz'],
        'application/x-gzpostscript' => ['ps.gz'],
        'application/x-hdf' => ['hdf', 'hdf4', 'h4', 'hdf5', 'h5'],
        'application/x-hfe-file' => ['hfe'],
        'application/x-hfe-floppy-image' => ['hfe'],
        'application/x-httpd-php' => ['php'],
        'application/x-hwp' => ['hwp'],
        'application/x-hwt' => ['hwt'],
        'application/x-ica' => ['ica'],
        'application/x-install-instructions' => ['install'],
        'application/x-ips-patch' => ['ips'],
        'application/x-ipynb+json' => ['ipynb'],
        'application/x-iso9660-appimage' => ['appimage'],
        'application/x-iso9660-image' => ['iso', 'iso9660'],
        'application/x-it87' => ['it87'],
        'application/x-iwork-keynote-sffkey' => ['key'],
        'application/x-iwork-numbers-sffnumbers' => ['numbers'],
        'application/x-iwork-pages-sffpages' => ['pages'],
        'application/x-jar' => ['jar'],
        'application/x-java' => ['class'],
        'application/x-java-archive' => ['jar'],
        'application/x-java-archive-diff' => ['jardiff'],
        'application/x-java-class' => ['class'],
        'application/x-java-jce-keystore' => ['jceks'],
        'application/x-java-jnlp-file' => ['jnlp'],
        'application/x-java-keystore' => ['jks', 'ks'],
        'application/x-java-pack200' => ['pack'],
        'application/x-java-vm' => ['class'],
        'application/x-javascript' => ['js', 'jsm', 'mjs'],
        'application/x-jbuilder-project' => ['jpr', 'jpx'],
        'application/x-karbon' => ['karbon'],
        'application/x-kchart' => ['chrt'],
        'application/x-keepass2' => ['kdbx'],
        'application/x-kexi-connectiondata' => ['kexic'],
        'application/x-kexiproject-shortcut' => ['kexis'],
        'application/x-kexiproject-sqlite' => ['kexi'],
        'application/x-kexiproject-sqlite2' => ['kexi'],
        'application/x-kexiproject-sqlite3' => ['kexi'],
        'application/x-kformula' => ['kfo'],
        'application/x-killustrator' => ['kil'],
        'application/x-kivio' => ['flw'],
        'application/x-kontour' => ['kon'],
        'application/x-kpovmodeler' => ['kpm'],
        'application/x-kpresenter' => ['kpr', 'kpt'],
        'application/x-krita' => ['kra', 'krz'],
        'application/x-kspread' => ['ksp'],
        'application/x-kugar' => ['kud'],
        'application/x-kword' => ['kwd', 'kwt'],
        'application/x-latex' => ['latex'],
        'application/x-lha' => ['lha', 'lzh'],
        'application/x-lhz' => ['lhz'],
        'application/x-linguist' => ['ts'],
        'application/x-lmdb' => ['mdb', 'lmdb'],
        'application/x-lotus123' => ['123', 'wk1', 'wk3', 'wk4', 'wks'],
        'application/x-lrzip' => ['lrz'],
        'application/x-lrzip-compressed-tar' => ['tar.lrz', 'tlrz'],
        'application/x-lua-bytecode' => ['luac'],
        'application/x-lyx' => ['lyx'],
        'application/x-lz4' => ['lz4'],
        'application/x-lz4-compressed-tar' => ['tar.lz4'],
        'application/x-lzh-compressed' => ['lzh', 'lha'],
        'application/x-lzip' => ['lz'],
        'application/x-lzip-compressed-tar' => ['tar.lz'],
        'application/x-lzma' => ['lzma'],
        'application/x-lzma-compressed-tar' => ['tar.lzma', 'tlz'],
        'application/x-lzop' => ['lzo'],
        'application/x-lzpdf' => ['pdf.lz'],
        'application/x-m4' => ['m4'],
        'application/x-magicpoint' => ['mgp'],
        'application/x-makeself' => ['run'],
        'application/x-mame-chd' => ['chd'],
        'application/x-markaby' => ['mab'],
        'application/x-mathematica' => ['nb'],
        'application/x-mdb' => ['mdb'],
        'application/x-mie' => ['mie'],
        'application/x-mif' => ['mif'],
        'application/x-mimearchive' => ['mhtml', 'mht'],
        'application/x-mobi8-ebook' => ['azw3', 'kfx'],
        'application/x-mobipocket-ebook' => ['prc', 'mobi'],
        'application/x-modrinth-modpack+zip' => ['mrpack'],
        'application/x-ms-application' => ['application'],
        'application/x-ms-asx' => ['asx', 'wax', 'wvx', 'wmx'],
        'application/x-ms-dos-executable' => ['exe', 'dll', 'cpl', 'drv', 'scr'],
        'application/x-ms-ne-executable' => ['exe', 'dll', 'cpl', 'drv', 'scr'],
        'application/x-ms-pdb' => ['pdb'],
        'application/x-ms-shortcut' => ['lnk'],
        'application/x-ms-wim' => ['wim', 'swm'],
        'application/x-ms-wmd' => ['wmd'],
        'application/x-ms-wmz' => ['wmz'],
        'application/x-ms-xbap' => ['xbap'],
        'application/x-msaccess' => ['mdb'],
        'application/x-msbinder' => ['obd'],
        'application/x-mscardfile' => ['crd'],
        'application/x-msclip' => ['clp'],
        'application/x-msdos-program' => ['exe'],
        'application/x-msdownload' => ['exe', 'dll', 'com', 'bat', 'msi', 'cpl', 'drv', 'scr'],
        'application/x-msexcel' => ['xls', 'xlc', 'xll', 'xlm', 'xlw', 'xla', 'xlt', 'xld'],
        'application/x-msi' => ['msi'],
        'application/x-msmediaview' => ['mvb', 'm13', 'm14'],
        'application/x-msmetafile' => ['wmf', 'wmz', 'emf', 'emz'],
        'application/x-msmoney' => ['mny'],
        'application/x-mspowerpoint' => ['ppz', 'ppt', 'pps', 'pot'],
        'application/x-mspublisher' => ['pub'],
        'application/x-msschedule' => ['scd'],
        'application/x-msterminal' => ['trm'],
        'application/x-mswinurl' => ['url'],
        'application/x-msword' => ['doc'],
        'application/x-mswrite' => ['wri'],
        'application/x-msx-rom' => ['msx'],
        'application/x-n64-rom' => ['n64', 'z64', 'v64'],
        'application/x-navi-animation' => ['ani'],
        'application/x-neo-geo-pocket-color-rom' => ['ngc'],
        'application/x-neo-geo-pocket-rom' => ['ngp'],
        'application/x-nes-rom' => ['nes', 'nez', 'unf', 'unif'],
        'application/x-netcdf' => ['nc', 'cdf'],
        'application/x-netshow-channel' => ['nsc'],
        'application/x-nintendo-3ds-executable' => ['3dsx'],
        'application/x-nintendo-3ds-rom' => ['3ds', 'cci'],
        'application/x-nintendo-ds-rom' => ['nds'],
        'application/x-nintendo-switch-xci' => ['xci'],
        'application/x-ns-proxy-autoconfig' => ['pac'],
        'application/x-nuscript' => ['nu'],
        'application/x-nx-xci' => ['xci'],
        'application/x-nzb' => ['nzb'],
        'application/x-object' => ['o', 'mod'],
        'application/x-ogg' => ['ogx'],
        'application/x-oleo' => ['oleo'],
        'application/x-openvpn-profile' => ['openvpn', 'ovpn'],
        'application/x-openzim' => ['zim'],
        'application/x-pagemaker' => ['p65', 'pm', 'pm6', 'pmd'],
        'application/x-pak' => ['pak'],
        'application/x-palm-database' => ['prc', 'pdb', 'pqa', 'oprc'],
        'application/x-par2' => ['PAR2', 'par2'],
        'application/x-parquet' => ['parquet'],
        'application/x-partial-download' => ['wkdownload', 'crdownload', 'part'],
        'application/x-pc-engine-rom' => ['pce'],
        'application/x-pcap' => ['pcap', 'cap', 'dmp'],
        'application/x-pcapng' => ['pcapng', 'ntar'],
        'application/x-pdf' => ['pdf'],
        'application/x-perl' => ['pl', 'pm', 'PL', 'al', 'perl', 'pod', 't'],
        'application/x-photoshop' => ['psd'],
        'application/x-php' => ['php', 'php3', 'php4', 'php5', 'phps'],
        'application/x-pilot' => ['prc', 'pdb'],
        'application/x-pkcs12' => ['p12', 'pfx'],
        'application/x-pkcs7-certificates' => ['p7b', 'spc'],
        'application/x-pkcs7-certreqresp' => ['p7r'],
        'application/x-planperfect' => ['pln'],
        'application/x-pocket-word' => ['psw'],
        'application/x-powershell' => ['ps1'],
        'application/x-pw' => ['pw'],
        'application/x-pyspread-bz-spreadsheet' => ['pys'],
        'application/x-pyspread-spreadsheet' => ['pysu'],
        'application/x-python-bytecode' => ['pyc', 'pyo'],
        'application/x-qbrew' => ['qbrew'],
        'application/x-qed-disk' => ['qed'],
        'application/x-qemu-disk' => ['qcow2', 'qcow'],
        'application/x-qpress' => ['qp'],
        'application/x-qtiplot' => ['qti', 'qti.gz'],
        'application/x-quattropro' => ['wb1', 'wb2', 'wb3', 'qpw'],
        'application/x-quicktime-media-link' => ['qtl'],
        'application/x-quicktimeplayer' => ['qtl'],
        'application/x-qw' => ['qif'],
        'application/x-rar' => ['rar'],
        'application/x-rar-compressed' => ['rar'],
        'application/x-raw-disk-image' => ['raw-disk-image', 'img'],
        'application/x-raw-disk-image-xz-compressed' => ['raw-disk-image.xz', 'img.xz'],
        'application/x-raw-floppy-disk-image' => ['fd', 'qd'],
        'application/x-redhat-package-manager' => ['rpm'],
        'application/x-reject' => ['rej'],
        'application/x-research-info-systems' => ['ris'],
        'application/x-rnc' => ['rnc'],
        'application/x-rpm' => ['rpm'],
        'application/x-ruby' => ['rb'],
        'application/x-rzip' => ['rz'],
        'application/x-rzip-compressed-tar' => ['tar.rz', 'trz'],
        'application/x-sami' => ['smi', 'sami'],
        'application/x-sap-file' => ['sap'],
        'application/x-saturn-rom' => ['iso'],
        'application/x-sdp' => ['sdp'],
        'application/x-sea' => ['sea'],
        'application/x-sega-cd-rom' => ['iso'],
        'application/x-sega-pico-rom' => ['iso'],
        'application/x-sg1000-rom' => ['sg'],
        'application/x-sh' => ['sh'],
        'application/x-shar' => ['shar'],
        'application/x-shared-library-la' => ['la'],
        'application/x-sharedlib' => ['so'],
        'application/x-shellscript' => ['sh'],
        'application/x-shockwave-flash' => ['swf', 'spl'],
        'application/x-shorten' => ['shn'],
        'application/x-siag' => ['siag'],
        'application/x-silverlight-app' => ['xap'],
        'application/x-sit' => ['sit'],
        'application/x-sitx' => ['sitx'],
        'application/x-smaf' => ['mmf', 'smaf'],
        'application/x-sms-rom' => ['sms'],
        'application/x-snes-rom' => ['sfc', 'smc'],
        'application/x-sony-bbeb' => ['lrf'],
        'application/x-source-rpm' => ['src.rpm', 'spm'],
        'application/x-spss-por' => ['por'],
        'application/x-spss-sav' => ['sav', 'zsav'],
        'application/x-spss-savefile' => ['sav', 'zsav'],
        'application/x-sql' => ['sql'],
        'application/x-sqlite2' => ['sqlite2'],
        'application/x-sqlite3' => ['sqlite3'],
        'application/x-srt' => ['srt'],
        'application/x-starcalc' => ['sdc'],
        'application/x-starchart' => ['sds'],
        'application/x-stardraw' => ['sda'],
        'application/x-starimpress' => ['sdd'],
        'application/x-starmail' => ['smd'],
        'application/x-starmath' => ['smf'],
        'application/x-starwriter' => ['sdw', 'vor'],
        'application/x-starwriter-global' => ['sgl'],
        'application/x-stuffit' => ['sit'],
        'application/x-stuffitx' => ['sitx'],
        'application/x-subrip' => ['srt'],
        'application/x-sv4cpio' => ['sv4cpio'],
        'application/x-sv4crc' => ['sv4crc'],
        'application/x-sylk' => ['sylk', 'slk'],
        'application/x-t3vm-image' => ['t3'],
        'application/x-t602' => ['602'],
        'application/x-tads' => ['gam'],
        'application/x-tar' => ['tar', 'gtar', 'gem'],
        'application/x-targa' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'application/x-tarz' => ['tar.Z', 'taz'],
        'application/x-tcl' => ['tcl', 'tk'],
        'application/x-tex' => ['tex', 'ltx', 'sty', 'cls', 'dtx', 'ins', 'latex'],
        'application/x-tex-gf' => ['gf'],
        'application/x-tex-pk' => ['pk'],
        'application/x-tex-tfm' => ['tfm'],
        'application/x-texinfo' => ['texinfo', 'texi'],
        'application/x-tga' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'application/x-tgif' => ['obj'],
        'application/x-theme' => ['theme'],
        'application/x-thomson-cartridge-memo7' => ['m7'],
        'application/x-thomson-cassette' => ['k7'],
        'application/x-thomson-sap-image' => ['sap'],
        'application/x-tiled-tmx' => ['tmx'],
        'application/x-tiled-tsx' => ['tsx'],
        'application/x-trash' => ['bak', 'old', 'sik'],
        'application/x-trig' => ['trig'],
        'application/x-troff' => ['tr', 'roff', 't'],
        'application/x-troff-man' => ['man'],
        'application/x-tzo' => ['tar.lzo', 'tzo'],
        'application/x-ufraw' => ['ufraw'],
        'application/x-ustar' => ['ustar'],
        'application/x-vdi-disk' => ['vdi'],
        'application/x-vhd-disk' => ['vhd', 'vpc'],
        'application/x-vhdx-disk' => ['vhdx'],
        'application/x-virtual-boy-rom' => ['vb'],
        'application/x-virtualbox-hdd' => ['hdd'],
        'application/x-virtualbox-ova' => ['ova'],
        'application/x-virtualbox-ovf' => ['ovf'],
        'application/x-virtualbox-vbox' => ['vbox'],
        'application/x-virtualbox-vbox-extpack' => ['vbox-extpack'],
        'application/x-virtualbox-vdi' => ['vdi'],
        'application/x-virtualbox-vhd' => ['vhd', 'vpc'],
        'application/x-virtualbox-vhdx' => ['vhdx'],
        'application/x-virtualbox-vmdk' => ['vmdk'],
        'application/x-vmdk-disk' => ['vmdk'],
        'application/x-vnd.kde.kexi' => ['kexi'],
        'application/x-wais-source' => ['src'],
        'application/x-wbfs' => ['iso'],
        'application/x-web-app-manifest+json' => ['webapp'],
        'application/x-wia' => ['iso'],
        'application/x-wii-iso-image' => ['iso'],
        'application/x-wii-rom' => ['iso'],
        'application/x-wii-wad' => ['wad'],
        'application/x-win-lnk' => ['lnk'],
        'application/x-windows-themepack' => ['themepack'],
        'application/x-wmf' => ['wmf'],
        'application/x-wonderswan-color-rom' => ['wsc'],
        'application/x-wonderswan-rom' => ['ws'],
        'application/x-wordperfect' => ['wp', 'wp4', 'wp5', 'wp6', 'wpd', 'wpp'],
        'application/x-wpg' => ['wpg'],
        'application/x-wwf' => ['wwf'],
        'application/x-x509-ca-cert' => ['der', 'crt', 'pem', 'cert'],
        'application/x-xar' => ['xar', 'pkg'],
        'application/x-xbel' => ['xbel'],
        'application/x-xfig' => ['fig'],
        'application/x-xliff' => ['xlf', 'xliff'],
        'application/x-xliff+xml' => ['xlf'],
        'application/x-xpinstall' => ['xpi'],
        'application/x-xspf+xml' => ['xspf'],
        'application/x-xz' => ['xz'],
        'application/x-xz-compressed-tar' => ['tar.xz', 'txz'],
        'application/x-xzpdf' => ['pdf.xz'],
        'application/x-yaml' => ['yaml', 'yml'],
        'application/x-zip' => ['zip', 'zipx'],
        'application/x-zip-compressed' => ['zip', 'zipx'],
        'application/x-zip-compressed-fb2' => ['fb2.zip'],
        'application/x-zmachine' => ['z1', 'z2', 'z3', 'z4', 'z5', 'z6', 'z7', 'z8'],
        'application/x-zoo' => ['zoo'],
        'application/x-zpaq' => ['zpaq'],
        'application/x-zstd-compressed-tar' => ['tar.zst', 'tzst'],
        'application/xaml+xml' => ['xaml'],
        'application/xcap-att+xml' => ['xav'],
        'application/xcap-caps+xml' => ['xca'],
        'application/xcap-diff+xml' => ['xdf'],
        'application/xcap-el+xml' => ['xel'],
        'application/xcap-error+xml' => ['xer'],
        'application/xcap-ns+xml' => ['xns'],
        'application/xenc+xml' => ['xenc'],
        'application/xfdf' => ['xfdf'],
        'application/xhtml+xml' => ['xhtml', 'xht', 'html', 'htm'],
        'application/xliff+xml' => ['xlf', 'xliff'],
        'application/xml' => ['xml', 'xsl', 'xsd', 'rng', 'xbl'],
        'application/xml-dtd' => ['dtd'],
        'application/xml-external-parsed-entity' => ['ent'],
        'application/xop+xml' => ['xop'],
        'application/xproc+xml' => ['xpl'],
        'application/xps' => ['xps'],
        'application/xslt+xml' => ['xsl', 'xslt'],
        'application/xspf+xml' => ['xspf'],
        'application/xv+xml' => ['mxml', 'xhvml', 'xvml', 'xvm'],
        'application/yaml' => ['yaml', 'yml'],
        'application/yang' => ['yang'],
        'application/yin+xml' => ['yin'],
        'application/zip' => ['zip', 'zipx'],
        'application/zlib' => ['zz'],
        'application/zstd' => ['zst'],
        'audio/3gpp' => ['3gpp', '3gp', '3ga'],
        'audio/3gpp-encrypted' => ['3gp', '3gpp', '3ga'],
        'audio/3gpp2' => ['3g2', '3gp2', '3gpp2'],
        'audio/aac' => ['aac', 'adts', 'ass'],
        'audio/ac3' => ['ac3'],
        'audio/adpcm' => ['adp'],
        'audio/amr' => ['amr'],
        'audio/amr-encrypted' => ['amr'],
        'audio/amr-wb' => ['awb'],
        'audio/amr-wb-encrypted' => ['awb'],
        'audio/annodex' => ['axa'],
        'audio/basic' => ['au', 'snd'],
        'audio/dff' => ['dff'],
        'audio/dsd' => ['dsf'],
        'audio/dsf' => ['dsf'],
        'audio/flac' => ['flac'],
        'audio/imelody' => ['imy', 'ime'],
        'audio/m3u' => ['m3u', 'm3u8', 'vlc'],
        'audio/m4a' => ['m4a', 'f4a'],
        'audio/midi' => ['mid', 'midi', 'kar', 'rmi'],
        'audio/mobile-xmf' => ['mxmf'],
        'audio/mp2' => ['mp2'],
        'audio/mp3' => ['mp3', 'mpga'],
        'audio/mp4' => ['m4a', 'mp4a', 'f4a'],
        'audio/mpeg' => ['mp3', 'mpga', 'mp2', 'mp2a', 'm2a', 'm3a'],
        'audio/mpegurl' => ['m3u', 'm3u8', 'vlc'],
        'audio/ogg' => ['ogg', 'oga', 'spx', 'opus'],
        'audio/prs.sid' => ['sid', 'psid'],
        'audio/s3m' => ['s3m'],
        'audio/scpls' => ['pls'],
        'audio/silk' => ['sil'],
        'audio/tta' => ['tta'],
        'audio/usac' => ['loas', 'xhe'],
        'audio/vnd.audible' => ['aa', 'aax'],
        'audio/vnd.audible.aax' => ['aax'],
        'audio/vnd.audible.aaxc' => ['aaxc'],
        'audio/vnd.dece.audio' => ['uva', 'uvva'],
        'audio/vnd.digital-winds' => ['eol'],
        'audio/vnd.dra' => ['dra'],
        'audio/vnd.dts' => ['dts'],
        'audio/vnd.dts.hd' => ['dtshd'],
        'audio/vnd.lucent.voice' => ['lvp'],
        'audio/vnd.m-realaudio' => ['ra', 'rax'],
        'audio/vnd.ms-playready.media.pya' => ['pya'],
        'audio/vnd.nokia.mobile-xmf' => ['mxmf'],
        'audio/vnd.nuera.ecelp4800' => ['ecelp4800'],
        'audio/vnd.nuera.ecelp7470' => ['ecelp7470'],
        'audio/vnd.nuera.ecelp9600' => ['ecelp9600'],
        'audio/vnd.rip' => ['rip'],
        'audio/vnd.rn-realaudio' => ['ra', 'rax'],
        'audio/vnd.wave' => ['wav'],
        'audio/vorbis' => ['oga', 'ogg'],
        'audio/wav' => ['wav'],
        'audio/wave' => ['wav'],
        'audio/webm' => ['weba'],
        'audio/wma' => ['wma'],
        'audio/x-aac' => ['aac', 'adts', 'ass'],
        'audio/x-aifc' => ['aifc', 'aiffc'],
        'audio/x-aiff' => ['aif', 'aiff', 'aifc'],
        'audio/x-aiffc' => ['aifc', 'aiffc'],
        'audio/x-amzxml' => ['amz'],
        'audio/x-annodex' => ['axa'],
        'audio/x-ape' => ['ape'],
        'audio/x-caf' => ['caf'],
        'audio/x-dff' => ['dff'],
        'audio/x-dsd' => ['dsf'],
        'audio/x-dsf' => ['dsf'],
        'audio/x-dts' => ['dts'],
        'audio/x-dtshd' => ['dtshd'],
        'audio/x-flac' => ['flac'],
        'audio/x-flac+ogg' => ['oga', 'ogg'],
        'audio/x-gsm' => ['gsm'],
        'audio/x-hx-aac-adts' => ['aac', 'adts', 'ass'],
        'audio/x-imelody' => ['imy', 'ime'],
        'audio/x-iriver-pla' => ['pla'],
        'audio/x-it' => ['it'],
        'audio/x-m3u' => ['m3u', 'm3u8', 'vlc'],
        'audio/x-m4a' => ['m4a', 'f4a'],
        'audio/x-m4b' => ['m4b', 'f4b'],
        'audio/x-m4r' => ['m4r'],
        'audio/x-matroska' => ['mka'],
        'audio/x-midi' => ['mid', 'midi', 'kar'],
        'audio/x-minipsf' => ['minipsf'],
        'audio/x-mo3' => ['mo3'],
        'audio/x-mod' => ['mod', 'ult', 'uni', 'm15', 'mtm', '669', 'med'],
        'audio/x-mp2' => ['mp2'],
        'audio/x-mp3' => ['mp3', 'mpga'],
        'audio/x-mp3-playlist' => ['m3u', 'm3u8', 'vlc'],
        'audio/x-mpeg' => ['mp3', 'mpga'],
        'audio/x-mpegurl' => ['m3u', 'm3u8', 'vlc'],
        'audio/x-mpg' => ['mp3', 'mpga'],
        'audio/x-ms-asx' => ['asx', 'wax', 'wvx', 'wmx'],
        'audio/x-ms-wax' => ['wax'],
        'audio/x-ms-wma' => ['wma'],
        'audio/x-ms-wmv' => ['wmv'],
        'audio/x-musepack' => ['mpc', 'mpp', 'mp+'],
        'audio/x-ogg' => ['oga', 'ogg', 'opus'],
        'audio/x-oggflac' => ['oga', 'ogg'],
        'audio/x-opus+ogg' => ['opus'],
        'audio/x-pn-audibleaudio' => ['aa', 'aax'],
        'audio/x-pn-realaudio' => ['ram', 'ra', 'rax'],
        'audio/x-pn-realaudio-plugin' => ['rmp'],
        'audio/x-psf' => ['psf'],
        'audio/x-psflib' => ['psflib'],
        'audio/x-realaudio' => ['ra'],
        'audio/x-rn-3gpp-amr' => ['3gp', '3gpp', '3ga'],
        'audio/x-rn-3gpp-amr-encrypted' => ['3gp', '3gpp', '3ga'],
        'audio/x-rn-3gpp-amr-wb' => ['3gp', '3gpp', '3ga'],
        'audio/x-rn-3gpp-amr-wb-encrypted' => ['3gp', '3gpp', '3ga'],
        'audio/x-s3m' => ['s3m'],
        'audio/x-scpls' => ['pls'],
        'audio/x-shorten' => ['shn'],
        'audio/x-speex' => ['spx'],
        'audio/x-speex+ogg' => ['oga', 'ogg', 'spx'],
        'audio/x-stm' => ['stm'],
        'audio/x-tak' => ['tak'],
        'audio/x-tta' => ['tta'],
        'audio/x-voc' => ['voc'],
        'audio/x-vorbis' => ['oga', 'ogg'],
        'audio/x-vorbis+ogg' => ['oga', 'ogg'],
        'audio/x-wav' => ['wav'],
        'audio/x-wavpack' => ['wv', 'wvp'],
        'audio/x-wavpack-correction' => ['wvc'],
        'audio/x-xi' => ['xi'],
        'audio/x-xm' => ['xm'],
        'audio/x-xmf' => ['xmf'],
        'audio/xm' => ['xm'],
        'audio/xmf' => ['xmf'],
        'chemical/x-cdx' => ['cdx'],
        'chemical/x-cif' => ['cif'],
        'chemical/x-cmdf' => ['cmdf'],
        'chemical/x-cml' => ['cml'],
        'chemical/x-csml' => ['csml'],
        'chemical/x-pdb' => ['pdb', 'brk'],
        'chemical/x-xyz' => ['xyz'],
        'flv-application/octet-stream' => ['flv'],
        'font/collection' => ['ttc'],
        'font/otf' => ['otf'],
        'font/ttf' => ['ttf'],
        'font/woff' => ['woff'],
        'font/woff2' => ['woff2'],
        'image/aces' => ['exr'],
        'image/apng' => ['apng', 'png'],
        'image/astc' => ['astc'],
        'image/avci' => ['avci'],
        'image/avcs' => ['avcs'],
        'image/avif' => ['avif', 'avifs'],
        'image/avif-sequence' => ['avif', 'avifs'],
        'image/bmp' => ['bmp', 'dib'],
        'image/cdr' => ['cdr'],
        'image/cgm' => ['cgm'],
        'image/dicom-rle' => ['drle'],
        'image/dpx' => ['dpx'],
        'image/emf' => ['emf'],
        'image/fax-g3' => ['g3'],
        'image/fits' => ['fits', 'fit', 'fts'],
        'image/g3fax' => ['g3'],
        'image/gif' => ['gif'],
        'image/heic' => ['heic', 'heif', 'hif'],
        'image/heic-sequence' => ['heics', 'heic', 'heif', 'hif'],
        'image/heif' => ['heif', 'heic', 'hif'],
        'image/heif-sequence' => ['heifs', 'heic', 'heif', 'hif'],
        'image/hej2k' => ['hej2'],
        'image/hsj2' => ['hsj2'],
        'image/ico' => ['ico'],
        'image/icon' => ['ico'],
        'image/ief' => ['ief'],
        'image/jls' => ['jls'],
        'image/jp2' => ['jp2', 'jpg2'],
        'image/jpeg' => ['jpg', 'jpeg', 'jpe', 'jfif'],
        'image/jpeg2000' => ['jp2', 'jpg2'],
        'image/jpeg2000-image' => ['jp2', 'jpg2'],
        'image/jph' => ['jph'],
        'image/jphc' => ['jhc'],
        'image/jpm' => ['jpm', 'jpgm'],
        'image/jpx' => ['jpx', 'jpf'],
        'image/jxl' => ['jxl'],
        'image/jxr' => ['jxr', 'hdp', 'wdp'],
        'image/jxra' => ['jxra'],
        'image/jxrs' => ['jxrs'],
        'image/jxs' => ['jxs'],
        'image/jxsc' => ['jxsc'],
        'image/jxsi' => ['jxsi'],
        'image/jxss' => ['jxss'],
        'image/ktx' => ['ktx'],
        'image/ktx2' => ['ktx2'],
        'image/openraster' => ['ora'],
        'image/pdf' => ['pdf'],
        'image/photoshop' => ['psd'],
        'image/pjpeg' => ['jpg', 'jpeg', 'jpe', 'jfif'],
        'image/png' => ['png'],
        'image/prs.btif' => ['btif', 'btf'],
        'image/prs.pti' => ['pti'],
        'image/psd' => ['psd'],
        'image/qoi' => ['qoi'],
        'image/rle' => ['rle'],
        'image/sgi' => ['sgi'],
        'image/svg' => ['svg'],
        'image/svg+xml' => ['svg', 'svgz'],
        'image/svg+xml-compressed' => ['svgz', 'svg.gz'],
        'image/t38' => ['t38'],
        'image/targa' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'image/tga' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'image/tiff' => ['tif', 'tiff'],
        'image/tiff-fx' => ['tfx'],
        'image/vnd.adobe.photoshop' => ['psd'],
        'image/vnd.airzip.accelerator.azv' => ['azv'],
        'image/vnd.dece.graphic' => ['uvi', 'uvvi', 'uvg', 'uvvg'],
        'image/vnd.djvu' => ['djvu', 'djv'],
        'image/vnd.djvu+multipage' => ['djvu', 'djv'],
        'image/vnd.dvb.subtitle' => ['sub'],
        'image/vnd.dwg' => ['dwg'],
        'image/vnd.dxf' => ['dxf'],
        'image/vnd.fastbidsheet' => ['fbs'],
        'image/vnd.fpx' => ['fpx'],
        'image/vnd.fst' => ['fst'],
        'image/vnd.fujixerox.edmics-mmr' => ['mmr'],
        'image/vnd.fujixerox.edmics-rlc' => ['rlc'],
        'image/vnd.microsoft.icon' => ['ico'],
        'image/vnd.mozilla.apng' => ['apng', 'png'],
        'image/vnd.ms-dds' => ['dds'],
        'image/vnd.ms-modi' => ['mdi'],
        'image/vnd.ms-photo' => ['wdp', 'jxr', 'hdp'],
        'image/vnd.net-fpx' => ['npx'],
        'image/vnd.pco.b16' => ['b16'],
        'image/vnd.rn-realpix' => ['rp'],
        'image/vnd.tencent.tap' => ['tap'],
        'image/vnd.valve.source.texture' => ['vtf'],
        'image/vnd.wap.wbmp' => ['wbmp'],
        'image/vnd.xiff' => ['xif'],
        'image/vnd.zbrush.pcx' => ['pcx'],
        'image/webp' => ['webp'],
        'image/wmf' => ['wmf'],
        'image/x-3ds' => ['3ds'],
        'image/x-adobe-dng' => ['dng'],
        'image/x-applix-graphics' => ['ag'],
        'image/x-bmp' => ['bmp', 'dib'],
        'image/x-bzeps' => ['eps.bz2', 'epsi.bz2', 'epsf.bz2'],
        'image/x-canon-cr2' => ['cr2'],
        'image/x-canon-cr3' => ['cr3'],
        'image/x-canon-crw' => ['crw'],
        'image/x-cdr' => ['cdr'],
        'image/x-cmu-raster' => ['ras'],
        'image/x-cmx' => ['cmx'],
        'image/x-compressed-xcf' => ['xcf.gz', 'xcf.bz2'],
        'image/x-dds' => ['dds'],
        'image/x-djvu' => ['djvu', 'djv'],
        'image/x-emf' => ['emf'],
        'image/x-eps' => ['eps', 'epsi', 'epsf'],
        'image/x-exr' => ['exr'],
        'image/x-fits' => ['fits', 'fit', 'fts'],
        'image/x-fpx' => ['fpx'],
        'image/x-freehand' => ['fh', 'fhc', 'fh4', 'fh5', 'fh7'],
        'image/x-fuji-raf' => ['raf'],
        'image/x-gimp-gbr' => ['gbr'],
        'image/x-gimp-gih' => ['gih'],
        'image/x-gimp-pat' => ['pat'],
        'image/x-gzeps' => ['eps.gz', 'epsi.gz', 'epsf.gz'],
        'image/x-icb' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'image/x-icns' => ['icns'],
        'image/x-ico' => ['ico'],
        'image/x-icon' => ['ico'],
        'image/x-iff' => ['iff', 'ilbm', 'lbm'],
        'image/x-ilbm' => ['iff', 'ilbm', 'lbm'],
        'image/x-jng' => ['jng'],
        'image/x-jp2-codestream' => ['j2c', 'j2k', 'jpc'],
        'image/x-jpeg2000-image' => ['jp2', 'jpg2'],
        'image/x-kiss-cel' => ['cel', 'kcf'],
        'image/x-kodak-dcr' => ['dcr'],
        'image/x-kodak-k25' => ['k25'],
        'image/x-kodak-kdc' => ['kdc'],
        'image/x-lwo' => ['lwo', 'lwob'],
        'image/x-lws' => ['lws'],
        'image/x-macpaint' => ['pntg'],
        'image/x-minolta-mrw' => ['mrw'],
        'image/x-mrsid-image' => ['sid'],
        'image/x-ms-bmp' => ['bmp', 'dib'],
        'image/x-msod' => ['msod'],
        'image/x-nikon-nef' => ['nef'],
        'image/x-nikon-nrw' => ['nrw'],
        'image/x-olympus-orf' => ['orf'],
        'image/x-panasonic-raw' => ['raw'],
        'image/x-panasonic-raw2' => ['rw2'],
        'image/x-panasonic-rw' => ['raw'],
        'image/x-panasonic-rw2' => ['rw2'],
        'image/x-pcx' => ['pcx'],
        'image/x-pentax-pef' => ['pef'],
        'image/x-pfm' => ['pfm'],
        'image/x-photo-cd' => ['pcd'],
        'image/x-photoshop' => ['psd'],
        'image/x-pict' => ['pic', 'pct', 'pict', 'pict1', 'pict2'],
        'image/x-portable-anymap' => ['pnm'],
        'image/x-portable-bitmap' => ['pbm'],
        'image/x-portable-graymap' => ['pgm'],
        'image/x-portable-pixmap' => ['ppm'],
        'image/x-psd' => ['psd'],
        'image/x-pxr' => ['pxr'],
        'image/x-quicktime' => ['qtif', 'qif'],
        'image/x-rgb' => ['rgb'],
        'image/x-sct' => ['sct'],
        'image/x-sgi' => ['sgi'],
        'image/x-sigma-x3f' => ['x3f'],
        'image/x-skencil' => ['sk', 'sk1'],
        'image/x-sony-arw' => ['arw'],
        'image/x-sony-sr2' => ['sr2'],
        'image/x-sony-srf' => ['srf'],
        'image/x-sun-raster' => ['sun'],
        'image/x-targa' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'image/x-tga' => ['tga', 'icb', 'tpic', 'vda', 'vst'],
        'image/x-win-bitmap' => ['cur'],
        'image/x-win-metafile' => ['wmf'],
        'image/x-wmf' => ['wmf'],
        'image/x-xbitmap' => ['xbm'],
        'image/x-xcf' => ['xcf'],
        'image/x-xfig' => ['fig'],
        'image/x-xpixmap' => ['xpm'],
        'image/x-xpm' => ['xpm'],
        'image/x-xwindowdump' => ['xwd'],
        'image/x.djvu' => ['djvu', 'djv'],
        'message/disposition-notification' => ['disposition-notification'],
        'message/global' => ['u8msg'],
        'message/global-delivery-status' => ['u8dsn'],
        'message/global-disposition-notification' => ['u8mdn'],
        'message/global-headers' => ['u8hdr'],
        'message/rfc822' => ['eml', 'mime'],
        'message/vnd.wfa.wsc' => ['wsc'],
        'model/3mf' => ['3mf'],
        'model/gltf+json' => ['gltf'],
        'model/gltf-binary' => ['glb'],
        'model/iges' => ['igs', 'iges'],
        'model/jt' => ['jt'],
        'model/mesh' => ['msh', 'mesh', 'silo'],
        'model/mtl' => ['mtl'],
        'model/obj' => ['obj'],
        'model/prc' => ['prc'],
        'model/step' => ['step', 'stp'],
        'model/step+xml' => ['stpx'],
        'model/step+zip' => ['stpz'],
        'model/step-xml+zip' => ['stpxz'],
        'model/stl' => ['stl'],
        'model/u3d' => ['u3d'],
        'model/vnd.bary' => ['bary'],
        'model/vnd.cld' => ['cld'],
        'model/vnd.collada+xml' => ['dae'],
        'model/vnd.dwf' => ['dwf'],
        'model/vnd.gdl' => ['gdl'],
        'model/vnd.gtw' => ['gtw'],
        'model/vnd.mts' => ['mts'],
        'model/vnd.opengex' => ['ogex'],
        'model/vnd.parasolid.transmit.binary' => ['x_b'],
        'model/vnd.parasolid.transmit.text' => ['x_t'],
        'model/vnd.pytha.pyox' => ['pyo', 'pyox'],
        'model/vnd.sap.vds' => ['vds'],
        'model/vnd.usda' => ['usda'],
        'model/vnd.usdz+zip' => ['usdz'],
        'model/vnd.valve.source.compiled-map' => ['bsp'],
        'model/vnd.vtu' => ['vtu'],
        'model/vrml' => ['wrl', 'vrml', 'vrm'],
        'model/x.stl-ascii' => ['stl'],
        'model/x.stl-binary' => ['stl'],
        'model/x3d+binary' => ['x3db', 'x3dbz'],
        'model/x3d+fastinfoset' => ['x3db'],
        'model/x3d+vrml' => ['x3dv', 'x3dvz'],
        'model/x3d+xml' => ['x3d', 'x3dz'],
        'model/x3d-vrml' => ['x3dv'],
        'text/cache-manifest' => ['appcache', 'manifest'],
        'text/calendar' => ['ics', 'ifb', 'vcs', 'icalendar'],
        'text/coffeescript' => ['coffee', 'litcoffee'],
        'text/crystal' => ['cr'],
        'text/css' => ['css'],
        'text/csv' => ['csv'],
        'text/csv-schema' => ['csvs'],
        'text/directory' => ['vcard', 'vcf', 'vct', 'gcrd'],
        'text/ecmascript' => ['es'],
        'text/gedcom' => ['ged', 'gedcom'],
        'text/google-video-pointer' => ['gvp'],
        'text/html' => ['html', 'htm', 'shtml'],
        'text/ico' => ['ico'],
        'text/jade' => ['jade'],
        'text/javascript' => ['js', 'mjs', 'jsm'],
        'text/jscript' => ['js', 'jsm', 'mjs'],
        'text/jscript.encode' => ['jse'],
        'text/jsx' => ['jsx'],
        'text/julia' => ['jl'],
        'text/less' => ['less'],
        'text/markdown' => ['md', 'markdown', 'mkd'],
        'text/mathml' => ['mml'],
        'text/mdx' => ['mdx'],
        'text/n3' => ['n3'],
        'text/org' => ['org'],
        'text/plain' => ['txt', 'text', 'conf', 'def', 'list', 'log', 'in', 'ini', 'asc'],
        'text/prs.lines.tag' => ['dsc'],
        'text/rdf' => ['rdf', 'rdfs', 'owl'],
        'text/richtext' => ['rtx'],
        'text/rss' => ['rss'],
        'text/rtf' => ['rtf'],
        'text/rust' => ['rs'],
        'text/sgml' => ['sgml', 'sgm'],
        'text/shex' => ['shex'],
        'text/slim' => ['slim', 'slm'],
        'text/spdx' => ['spdx'],
        'text/spreadsheet' => ['sylk', 'slk'],
        'text/stylus' => ['stylus', 'styl'],
        'text/tab-separated-values' => ['tsv'],
        'text/tcl' => ['tcl', 'tk'],
        'text/troff' => ['t', 'tr', 'roff', 'man', 'me', 'ms'],
        'text/turtle' => ['ttl'],
        'text/uri-list' => ['uri', 'uris', 'urls'],
        'text/vbs' => ['vbs'],
        'text/vbscript' => ['vbs'],
        'text/vbscript.encode' => ['vbe'],
        'text/vcard' => ['vcard', 'vcf', 'vct', 'gcrd'],
        'text/vnd.curl' => ['curl'],
        'text/vnd.curl.dcurl' => ['dcurl'],
        'text/vnd.curl.mcurl' => ['mcurl'],
        'text/vnd.curl.scurl' => ['scurl'],
        'text/vnd.dvb.subtitle' => ['sub'],
        'text/vnd.familysearch.gedcom' => ['ged', 'gedcom'],
        'text/vnd.fly' => ['fly'],
        'text/vnd.fmi.flexstor' => ['flx'],
        'text/vnd.graphviz' => ['gv', 'dot'],
        'text/vnd.in3d.3dml' => ['3dml'],
        'text/vnd.in3d.spot' => ['spot'],
        'text/vnd.qt.linguist' => ['ts'],
        'text/vnd.rn-realtext' => ['rt'],
        'text/vnd.senx.warpscript' => ['mc2'],
        'text/vnd.sun.j2me.app-descriptor' => ['jad'],
        'text/vnd.trolltech.linguist' => ['ts'],
        'text/vnd.wap.wml' => ['wml'],
        'text/vnd.wap.wmlscript' => ['wmls'],
        'text/vtt' => ['vtt'],
        'text/wgsl' => ['wgsl'],
        'text/x-adasrc' => ['adb', 'ads'],
        'text/x-asm' => ['s', 'asm'],
        'text/x-basic' => ['bas'],
        'text/x-bibtex' => ['bib'],
        'text/x-blueprint' => ['blp'],
        'text/x-c' => ['c', 'cc', 'cxx', 'cpp', 'h', 'hh', 'dic'],
        'text/x-c++hdr' => ['hh', 'hp', 'hpp', 'h++', 'hxx'],
        'text/x-c++src' => ['cpp', 'cxx', 'cc', 'C', 'c++'],
        'text/x-chdr' => ['h'],
        'text/x-cmake' => ['cmake'],
        'text/x-cobol' => ['cbl', 'cob'],
        'text/x-comma-separated-values' => ['csv'],
        'text/x-common-lisp' => ['asd', 'fasl', 'lisp', 'ros'],
        'text/x-component' => ['htc'],
        'text/x-crystal' => ['cr'],
        'text/x-csharp' => ['cs'],
        'text/x-csrc' => ['c'],
        'text/x-csv' => ['csv'],
        'text/x-cython' => ['pxd', 'pxi', 'pyx'],
        'text/x-dart' => ['dart'],
        'text/x-dbus-service' => ['service'],
        'text/x-dcl' => ['dcl'],
        'text/x-devicetree-binary' => ['dtb'],
        'text/x-devicetree-source' => ['dts', 'dtsi'],
        'text/x-diff' => ['diff', 'patch'],
        'text/x-dsl' => ['dsl'],
        'text/x-dsrc' => ['d', 'di'],
        'text/x-dtd' => ['dtd'],
        'text/x-eiffel' => ['e', 'eif'],
        'text/x-elixir' => ['ex', 'exs'],
        'text/x-emacs-lisp' => ['el'],
        'text/x-erlang' => ['erl'],
        'text/x-fish' => ['fish'],
        'text/x-fortran' => ['f', 'for', 'f77', 'f90', 'f95'],
        'text/x-gcode-gx' => ['gx'],
        'text/x-genie' => ['gs'],
        'text/x-gettext-translation' => ['po'],
        'text/x-gettext-translation-template' => ['pot'],
        'text/x-gherkin' => ['feature'],
        'text/x-go' => ['go'],
        'text/x-google-video-pointer' => ['gvp'],
        'text/x-gradle' => ['gradle'],
        'text/x-groovy' => ['groovy', 'gvy', 'gy', 'gsh'],
        'text/x-handlebars-template' => ['hbs'],
        'text/x-haskell' => ['hs'],
        'text/x-idl' => ['idl'],
        'text/x-imelody' => ['imy', 'ime'],
        'text/x-iptables' => ['iptables'],
        'text/x-java' => ['java'],
        'text/x-java-source' => ['java'],
        'text/x-kaitai-struct' => ['ksy'],
        'text/x-kotlin' => ['kt'],
        'text/x-ldif' => ['ldif'],
        'text/x-lilypond' => ['ly'],
        'text/x-literate-haskell' => ['lhs'],
        'text/x-log' => ['log'],
        'text/x-lua' => ['lua'],
        'text/x-lyx' => ['lyx'],
        'text/x-makefile' => ['mk', 'mak'],
        'text/x-markdown' => ['md', 'mkd', 'markdown'],
        'text/x-matlab' => ['m'],
        'text/x-microdvd' => ['sub'],
        'text/x-moc' => ['moc'],
        'text/x-modelica' => ['mo'],
        'text/x-mof' => ['mof'],
        'text/x-mpl2' => ['mpl'],
        'text/x-mpsub' => ['sub'],
        'text/x-mrml' => ['mrml', 'mrl'],
        'text/x-ms-regedit' => ['reg'],
        'text/x-mup' => ['mup', 'not'],
        'text/x-nfo' => ['nfo'],
        'text/x-nim' => ['nim'],
        'text/x-nimscript' => ['nims', 'nimble'],
        'text/x-nix' => ['nix'],
        'text/x-nu' => ['nu'],
        'text/x-objc++src' => ['mm'],
        'text/x-objcsrc' => ['m'],
        'text/x-ocaml' => ['ml', 'mli'],
        'text/x-ocl' => ['ocl'],
        'text/x-octave' => ['m'],
        'text/x-ooc' => ['ooc'],
        'text/x-opencl-src' => ['cl'],
        'text/x-opml' => ['opml'],
        'text/x-opml+xml' => ['opml'],
        'text/x-org' => ['org'],
        'text/x-pascal' => ['p', 'pas'],
        'text/x-patch' => ['diff', 'patch'],
        'text/x-perl' => ['pl', 'PL', 'pm', 'al', 'perl', 'pod', 't'],
        'text/x-po' => ['po'],
        'text/x-pot' => ['pot'],
        'text/x-processing' => ['pde'],
        'text/x-python' => ['py', 'wsgi'],
        'text/x-python2' => ['py', 'py2'],
        'text/x-python3' => ['py', 'py3', 'pyi'],
        'text/x-qml' => ['qml', 'qmltypes', 'qmlproject'],
        'text/x-reject' => ['rej'],
        'text/x-rpm-spec' => ['spec'],
        'text/x-rst' => ['rst'],
        'text/x-sagemath' => ['sage'],
        'text/x-sass' => ['sass'],
        'text/x-scala' => ['scala', 'sc'],
        'text/x-scheme' => ['scm', 'ss'],
        'text/x-scss' => ['scss'],
        'text/x-setext' => ['etx'],
        'text/x-sfv' => ['sfv'],
        'text/x-sh' => ['sh'],
        'text/x-sql' => ['sql'],
        'text/x-ssa' => ['ssa', 'ass'],
        'text/x-subviewer' => ['sub'],
        'text/x-suse-ymp' => ['ymp'],
        'text/x-svhdr' => ['svh'],
        'text/x-svsrc' => ['sv'],
        'text/x-systemd-unit' => ['automount', 'device', 'mount', 'path', 'scope', 'service', 'slice', 'socket', 'swap', 'target', 'timer'],
        'text/x-tcl' => ['tcl', 'tk'],
        'text/x-tex' => ['tex', 'ltx', 'sty', 'cls', 'dtx', 'ins', 'latex'],
        'text/x-texinfo' => ['texi', 'texinfo'],
        'text/x-troff' => ['tr', 'roff', 't'],
        'text/x-troff-me' => ['me'],
        'text/x-troff-mm' => ['mm'],
        'text/x-troff-ms' => ['ms'],
        'text/x-twig' => ['twig'],
        'text/x-txt2tags' => ['t2t'],
        'text/x-typst' => ['typ'],
        'text/x-uil' => ['uil'],
        'text/x-uuencode' => ['uu', 'uue'],
        'text/x-vala' => ['vala', 'vapi'],
        'text/x-vb' => ['vb'],
        'text/x-vcalendar' => ['vcs', 'ics', 'ifb', 'icalendar'],
        'text/x-vcard' => ['vcf', 'vcard', 'vct', 'gcrd'],
        'text/x-verilog' => ['v'],
        'text/x-vhdl' => ['vhd', 'vhdl'],
        'text/x-xmi' => ['xmi'],
        'text/x-xslfo' => ['fo', 'xslfo'],
        'text/x-yaml' => ['yaml', 'yml'],
        'text/x.gcode' => ['gcode'],
        'text/xml' => ['xml', 'xbl', 'xsd', 'rng'],
        'text/xml-external-parsed-entity' => ['ent'],
        'text/yaml' => ['yaml', 'yml'],
        'video/3gp' => ['3gp', '3gpp', '3ga'],
        'video/3gpp' => ['3gp', '3gpp', '3ga'],
        'video/3gpp-encrypted' => ['3gp', '3gpp', '3ga'],
        'video/3gpp2' => ['3g2', '3gp2', '3gpp2'],
        'video/annodex' => ['axv'],
        'video/avi' => ['avi', 'avf', 'divx'],
        'video/divx' => ['avi', 'avf', 'divx'],
        'video/dv' => ['dv'],
        'video/fli' => ['fli', 'flc'],
        'video/flv' => ['flv'],
        'video/h261' => ['h261'],
        'video/h263' => ['h263'],
        'video/h264' => ['h264'],
        'video/iso.segment' => ['m4s'],
        'video/jpeg' => ['jpgv'],
        'video/jpm' => ['jpm', 'jpgm'],
        'video/mj2' => ['mj2', 'mjp2'],
        'video/mp2t' => ['ts', 'm2t', 'm2ts', 'mts', 'cpi', 'clpi', 'mpl', 'mpls', 'bdm', 'bdmv'],
        'video/mp4' => ['mp4', 'mp4v', 'mpg4', 'm4v', 'f4v', 'lrv'],
        'video/mp4v-es' => ['mp4', 'm4v', 'f4v', 'lrv'],
        'video/mpeg' => ['mpeg', 'mpg', 'mpe', 'm1v', 'm2v', 'mp2', 'vob'],
        'video/mpeg-system' => ['mpeg', 'mpg', 'mp2', 'mpe', 'vob'],
        'video/mpg4' => ['mpg4'],
        'video/msvideo' => ['avi', 'avf', 'divx'],
        'video/ogg' => ['ogv', 'ogg'],
        'video/quicktime' => ['mov', 'qt', 'moov', 'qtvr'],
        'video/vivo' => ['viv', 'vivo'],
        'video/vnd.avi' => ['avi', 'avf', 'divx'],
        'video/vnd.dece.hd' => ['uvh', 'uvvh'],
        'video/vnd.dece.mobile' => ['uvm', 'uvvm'],
        'video/vnd.dece.pd' => ['uvp', 'uvvp'],
        'video/vnd.dece.sd' => ['uvs', 'uvvs'],
        'video/vnd.dece.video' => ['uvv', 'uvvv'],
        'video/vnd.divx' => ['avi', 'avf', 'divx'],
        'video/vnd.dvb.file' => ['dvb'],
        'video/vnd.fvt' => ['fvt'],
        'video/vnd.mpegurl' => ['mxu', 'm4u', 'm1u'],
        'video/vnd.ms-playready.media.pyv' => ['pyv'],
        'video/vnd.radgamettools.bink' => ['bik', 'bk2'],
        'video/vnd.radgamettools.smacker' => ['smk'],
        'video/vnd.rn-realvideo' => ['rv', 'rvx'],
        'video/vnd.uvvu.mp4' => ['uvu', 'uvvu'],
        'video/vnd.vivo' => ['viv', 'vivo'],
        'video/vnd.youtube.yt' => ['yt'],
        'video/webm' => ['webm'],
        'video/x-anim' => ['anim1', 'anim2', 'anim3', 'anim4', 'anim5', 'anim6', 'anim7', 'anim8', 'anim9', 'animj'],
        'video/x-annodex' => ['axv'],
        'video/x-avi' => ['avi', 'avf', 'divx'],
        'video/x-f4v' => ['f4v'],
        'video/x-fli' => ['fli', 'flc'],
        'video/x-flic' => ['fli', 'flc'],
        'video/x-flv' => ['flv'],
        'video/x-javafx' => ['fxm'],
        'video/x-m4v' => ['m4v', 'mp4', 'f4v', 'lrv'],
        'video/x-matroska' => ['mkv', 'mk3d', 'mks'],
        'video/x-matroska-3d' => ['mk3d'],
        'video/x-mjpeg' => ['mjpeg', 'mjpg'],
        'video/x-mng' => ['mng'],
        'video/x-mpeg' => ['mpeg', 'mpg', 'mp2', 'mpe', 'vob'],
        'video/x-mpeg-system' => ['mpeg', 'mpg', 'mp2', 'mpe', 'vob'],
        'video/x-mpeg2' => ['mpeg', 'mpg', 'mp2', 'mpe', 'vob'],
        'video/x-mpegurl' => ['m1u', 'm4u', 'mxu'],
        'video/x-ms-asf' => ['asf', 'asx'],
        'video/x-ms-asf-plugin' => ['asf'],
        'video/x-ms-vob' => ['vob'],
        'video/x-ms-wax' => ['asx', 'wax', 'wvx', 'wmx'],
        'video/x-ms-wm' => ['wm', 'asf'],
        'video/x-ms-wmv' => ['wmv'],
        'video/x-ms-wmx' => ['wmx', 'asx', 'wax', 'wvx'],
        'video/x-ms-wvx' => ['wvx', 'asx', 'wax', 'wmx'],
        'video/x-msvideo' => ['avi', 'avf', 'divx'],
        'video/x-nsv' => ['nsv'],
        'video/x-ogg' => ['ogv', 'ogg'],
        'video/x-ogm' => ['ogm'],
        'video/x-ogm+ogg' => ['ogm'],
        'video/x-real-video' => ['rv', 'rvx'],
        'video/x-sgi-movie' => ['movie'],
        'video/x-smv' => ['smv'],
        'video/x-theora' => ['ogg'],
        'video/x-theora+ogg' => ['ogg'],
        'x-conference/x-cooltalk' => ['ice'],
        'x-epoc/x-sisx-app' => ['sisx'],
        'zz-application/zz-winassoc-123' => ['123', 'wk1', 'wk3', 'wk4', 'wks'],
        'zz-application/zz-winassoc-cab' => ['cab'],
        'zz-application/zz-winassoc-cdr' => ['cdr'],
        'zz-application/zz-winassoc-doc' => ['doc'],
        'zz-application/zz-winassoc-hlp' => ['hlp'],
        'zz-application/zz-winassoc-mdb' => ['mdb'],
        'zz-application/zz-winassoc-uu' => ['uue'],
        'zz-application/zz-winassoc-xls' => ['xls', 'xlc', 'xll', 'xlm', 'xlw', 'xla', 'xlt', 'xld'],
    ];

    private const REVERSE_MAP = [
        '123' => ['application/lotus123', 'application/vnd.lotus-1-2-3', 'application/wk1', 'application/x-123', 'application/x-lotus123', 'zz-application/zz-winassoc-123'],
        '1km' => ['application/vnd.1000minds.decision-model+xml'],
        '32x' => ['application/x-genesis-32x-rom'],
        '3dml' => ['text/vnd.in3d.3dml'],
        '3ds' => ['application/x-nintendo-3ds-rom', 'image/x-3ds'],
        '3dsx' => ['application/x-nintendo-3ds-executable'],
        '3g2' => ['audio/3gpp2', 'video/3gpp2'],
        '3ga' => ['audio/3gpp', 'audio/3gpp-encrypted', 'audio/x-rn-3gpp-amr', 'audio/x-rn-3gpp-amr-encrypted', 'audio/x-rn-3gpp-amr-wb', 'audio/x-rn-3gpp-amr-wb-encrypted', 'video/3gp', 'video/3gpp', 'video/3gpp-encrypted'],
        '3gp' => ['audio/3gpp', 'audio/3gpp-encrypted', 'audio/x-rn-3gpp-amr', 'audio/x-rn-3gpp-amr-encrypted', 'audio/x-rn-3gpp-amr-wb', 'audio/x-rn-3gpp-amr-wb-encrypted', 'video/3gp', 'video/3gpp', 'video/3gpp-encrypted'],
        '3gp2' => ['audio/3gpp2', 'video/3gpp2'],
        '3gpp' => ['audio/3gpp', 'audio/3gpp-encrypted', 'audio/x-rn-3gpp-amr', 'audio/x-rn-3gpp-amr-encrypted', 'audio/x-rn-3gpp-amr-wb', 'audio/x-rn-3gpp-amr-wb-encrypted', 'video/3gp', 'video/3gpp', 'video/3gpp-encrypted'],
        '3gpp2' => ['audio/3gpp2', 'video/3gpp2'],
        '3mf' => ['application/vnd.ms-3mfdocument', 'model/3mf'],
        '602' => ['application/x-t602'],
        '669' => ['audio/x-mod'],
        '7z' => ['application/x-7z-compressed'],
        '7z.001' => ['application/x-7z-compressed'],
        'C' => ['text/x-c++src'],
        'PAR2' => ['application/x-par2'],
        'PL' => ['application/x-perl', 'text/x-perl'],
        'Z' => ['application/x-compress'],
        'a' => ['application/x-archive'],
        'a26' => ['application/x-atari-2600-rom'],
        'a78' => ['application/x-atari-7800-rom'],
        'aa' => ['audio/vnd.audible', 'audio/x-pn-audibleaudio'],
        'aab' => ['application/x-authorware-bin'],
        'aac' => ['audio/aac', 'audio/x-aac', 'audio/x-hx-aac-adts'],
        'aam' => ['application/x-authorware-map'],
        'aas' => ['application/x-authorware-seg'],
        'aax' => ['audio/vnd.audible', 'audio/vnd.audible.aax', 'audio/x-pn-audibleaudio'],
        'aaxc' => ['audio/vnd.audible.aaxc'],
        'abw' => ['application/x-abiword'],
        'abw.CRASHED' => ['application/x-abiword'],
        'abw.gz' => ['application/x-abiword'],
        'ac' => ['application/pkix-attr-cert', 'application/vnd.nokia.n-gage.ac+xml'],
        'ac3' => ['audio/ac3'],
        'acc' => ['application/vnd.americandynamics.acc'],
        'ace' => ['application/x-ace', 'application/x-ace-compressed'],
        'acu' => ['application/vnd.acucobol'],
        'acutc' => ['application/vnd.acucorp'],
        'adb' => ['text/x-adasrc'],
        'adf' => ['application/x-amiga-disk-format'],
        'adp' => ['audio/adpcm'],
        'ads' => ['text/x-adasrc'],
        'adts' => ['audio/aac', 'audio/x-aac', 'audio/x-hx-aac-adts'],
        'aep' => ['application/vnd.audiograph'],
        'afm' => ['application/x-font-afm', 'application/x-font-type1'],
        'afp' => ['application/vnd.ibm.modcap'],
        'ag' => ['image/x-applix-graphics'],
        'agb' => ['application/x-gba-rom'],
        'age' => ['application/vnd.age'],
        'ahead' => ['application/vnd.ahead.space'],
        'ai' => ['application/illustrator', 'application/postscript', 'application/vnd.adobe.illustrator'],
        'aif' => ['audio/x-aiff'],
        'aifc' => ['audio/x-aifc', 'audio/x-aiff', 'audio/x-aiffc'],
        'aiff' => ['audio/x-aiff'],
        'aiffc' => ['audio/x-aifc', 'audio/x-aiffc'],
        'air' => ['application/vnd.adobe.air-application-installer-package+zip'],
        'ait' => ['application/vnd.dvb.ait'],
        'al' => ['application/x-perl', 'text/x-perl'],
        'alz' => ['application/x-alz'],
        'ami' => ['application/vnd.amiga.ami'],
        'aml' => ['application/automationml-aml+xml'],
        'amlx' => ['application/automationml-amlx+zip'],
        'amr' => ['audio/amr', 'audio/amr-encrypted'],
        'amz' => ['audio/x-amzxml'],
        'ani' => ['application/x-navi-animation'],
        'anim1' => ['video/x-anim'],
        'anim2' => ['video/x-anim'],
        'anim3' => ['video/x-anim'],
        'anim4' => ['video/x-anim'],
        'anim5' => ['video/x-anim'],
        'anim6' => ['video/x-anim'],
        'anim7' => ['video/x-anim'],
        'anim8' => ['video/x-anim'],
        'anim9' => ['video/x-anim'],
        'animj' => ['video/x-anim'],
        'anx' => ['application/annodex', 'application/x-annodex'],
        'ape' => ['audio/x-ape'],
        'apk' => ['application/vnd.android.package-archive'],
        'apng' => ['image/apng', 'image/vnd.mozilla.apng'],
        'appcache' => ['text/cache-manifest'],
        'appimage' => ['application/vnd.appimage', 'application/x-iso9660-appimage'],
        'appinstaller' => ['application/appinstaller'],
        'application' => ['application/x-ms-application'],
        'appx' => ['application/appx'],
        'appxbundle' => ['application/appxbundle'],
        'apr' => ['application/vnd.lotus-approach'],
        'ar' => ['application/x-archive'],
        'arc' => ['application/x-freearc'],
        'arj' => ['application/x-arj'],
        'arw' => ['image/x-sony-arw'],
        'as' => ['application/x-applix-spreadsheet'],
        'asar' => ['application/x-asar'],
        'asc' => ['application/pgp', 'application/pgp-encrypted', 'application/pgp-keys', 'application/pgp-signature', 'text/plain'],
        'asd' => ['text/x-common-lisp'],
        'asf' => ['application/vnd.ms-asf', 'video/x-ms-asf', 'video/x-ms-asf-plugin', 'video/x-ms-wm'],
        'asice' => ['application/vnd.etsi.asic-e+zip'],
        'asm' => ['text/x-asm'],
        'aso' => ['application/vnd.accpac.simply.aso'],
        'asp' => ['application/x-asp'],
        'ass' => ['audio/aac', 'audio/x-aac', 'audio/x-hx-aac-adts', 'text/x-ssa'],
        'astc' => ['image/astc'],
        'asx' => ['application/x-ms-asx', 'audio/x-ms-asx', 'video/x-ms-asf', 'video/x-ms-wax', 'video/x-ms-wmx', 'video/x-ms-wvx'],
        'atc' => ['application/vnd.acucorp'],
        'atom' => ['application/atom+xml'],
        'atomcat' => ['application/atomcat+xml'],
        'atomdeleted' => ['application/atomdeleted+xml'],
        'atomsvc' => ['application/atomsvc+xml'],
        'atx' => ['application/vnd.antix.game-component'],
        'au' => ['audio/basic'],
        'automount' => ['text/x-systemd-unit'],
        'avci' => ['image/avci'],
        'avcs' => ['image/avcs'],
        'avf' => ['video/avi', 'video/divx', 'video/msvideo', 'video/vnd.avi', 'video/vnd.divx', 'video/x-avi', 'video/x-msvideo'],
        'avi' => ['video/avi', 'video/divx', 'video/msvideo', 'video/vnd.avi', 'video/vnd.divx', 'video/x-avi', 'video/x-msvideo'],
        'avif' => ['image/avif', 'image/avif-sequence'],
        'avifs' => ['image/avif', 'image/avif-sequence'],
        'aw' => ['application/applixware', 'application/x-applix-word'],
        'awb' => ['audio/amr-wb', 'audio/amr-wb-encrypted'],
        'awk' => ['application/x-awk'],
        'axa' => ['audio/annodex', 'audio/x-annodex'],
        'axv' => ['video/annodex', 'video/x-annodex'],
        'azf' => ['application/vnd.airzip.filesecure.azf'],
        'azs' => ['application/vnd.airzip.filesecure.azs'],
        'azv' => ['image/vnd.airzip.accelerator.azv'],
        'azw' => ['application/vnd.amazon.ebook'],
        'azw3' => ['application/vnd.amazon.mobi8-ebook', 'application/x-mobi8-ebook'],
        'b16' => ['image/vnd.pco.b16'],
        'bak' => ['application/x-trash'],
        'bary' => ['model/vnd.bary'],
        'bas' => ['text/x-basic'],
        'bat' => ['application/bat', 'application/x-bat', 'application/x-msdownload'],
        'bcpio' => ['application/x-bcpio'],
        'bdf' => ['application/x-font-bdf'],
        'bdm' => ['application/vnd.syncml.dm+wbxml', 'video/mp2t'],
        'bdmv' => ['video/mp2t'],
        'bdo' => ['application/vnd.nato.bindingdataobject+xml'],
        'bdoc' => ['application/bdoc', 'application/x-bdoc'],
        'bed' => ['application/vnd.realvnc.bed'],
        'bh2' => ['application/vnd.fujitsu.oasysprs'],
        'bib' => ['text/x-bibtex'],
        'bik' => ['video/vnd.radgamettools.bink'],
        'bin' => ['application/octet-stream'],
        'bk2' => ['video/vnd.radgamettools.bink'],
        'blb' => ['application/x-blorb'],
        'blend' => ['application/x-blender'],
        'blender' => ['application/x-blender'],
        'blorb' => ['application/x-blorb'],
        'blp' => ['text/x-blueprint'],
        'bmi' => ['application/vnd.bmi'],
        'bmml' => ['application/vnd.balsamiq.bmml+xml'],
        'bmp' => ['image/bmp', 'image/x-bmp', 'image/x-ms-bmp'],
        'book' => ['application/vnd.framemaker'],
        'box' => ['application/vnd.previewsystems.box'],
        'boz' => ['application/x-bzip2'],
        'bps' => ['application/x-bps-patch'],
        'brk' => ['chemical/x-pdb'],
        'bsdiff' => ['application/x-bsdiff'],
        'bsp' => ['model/vnd.valve.source.compiled-map'],
        'btf' => ['image/prs.btif'],
        'btif' => ['image/prs.btif'],
        'bz' => ['application/bzip2', 'application/x-bzip', 'application/x-bzip1'],
        'bz2' => ['application/x-bz2', 'application/bzip2', 'application/x-bzip', 'application/x-bzip2'],
        'bz3' => ['application/x-bzip3'],
        'c' => ['text/x-c', 'text/x-csrc'],
        'c++' => ['text/x-c++src'],
        'c11amc' => ['application/vnd.cluetrust.cartomobile-config'],
        'c11amz' => ['application/vnd.cluetrust.cartomobile-config-pkg'],
        'c4d' => ['application/vnd.clonk.c4group'],
        'c4f' => ['application/vnd.clonk.c4group'],
        'c4g' => ['application/vnd.clonk.c4group'],
        'c4p' => ['application/vnd.clonk.c4group'],
        'c4u' => ['application/vnd.clonk.c4group'],
        'cab' => ['application/vnd.ms-cab-compressed', 'zz-application/zz-winassoc-cab'],
        'caf' => ['audio/x-caf'],
        'cap' => ['application/pcap', 'application/vnd.tcpdump.pcap', 'application/x-pcap'],
        'car' => ['application/vnd.curl.car'],
        'cat' => ['application/vnd.ms-pki.seccat'],
        'cb7' => ['application/x-cb7', 'application/x-cbr'],
        'cba' => ['application/x-cbr'],
        'cbl' => ['text/x-cobol'],
        'cbor' => ['application/cbor'],
        'cbr' => ['application/vnd.comicbook-rar', 'application/x-cbr'],
        'cbt' => ['application/x-cbr', 'application/x-cbt'],
        'cbz' => ['application/vnd.comicbook+zip', 'application/x-cbr', 'application/x-cbz'],
        'cc' => ['text/x-c', 'text/x-c++src'],
        'cci' => ['application/x-nintendo-3ds-rom'],
        'ccmx' => ['application/x-ccmx'],
        'cco' => ['application/x-cocoa'],
        'cct' => ['application/x-director'],
        'ccxml' => ['application/ccxml+xml'],
        'cdbcmsg' => ['application/vnd.contact.cmsg'],
        'cdf' => ['application/x-netcdf'],
        'cdfx' => ['application/cdfx+xml'],
        'cdi' => ['application/x-discjuggler-cd-image'],
        'cdkey' => ['application/vnd.mediastation.cdkey'],
        'cdmia' => ['application/cdmi-capability'],
        'cdmic' => ['application/cdmi-container'],
        'cdmid' => ['application/cdmi-domain'],
        'cdmio' => ['application/cdmi-object'],
        'cdmiq' => ['application/cdmi-queue'],
        'cdr' => ['application/cdr', 'application/coreldraw', 'application/vnd.corel-draw', 'application/x-cdr', 'application/x-coreldraw', 'image/cdr', 'image/x-cdr', 'zz-application/zz-winassoc-cdr'],
        'cdx' => ['chemical/x-cdx'],
        'cdxml' => ['application/vnd.chemdraw+xml'],
        'cdy' => ['application/vnd.cinderella'],
        'cel' => ['image/x-kiss-cel'],
        'cer' => ['application/pkix-cert'],
        'cert' => ['application/x-x509-ca-cert'],
        'cfs' => ['application/x-cfs-compressed'],
        'cgb' => ['application/x-gameboy-color-rom'],
        'cgm' => ['image/cgm'],
        'chat' => ['application/x-chat'],
        'chd' => ['application/x-mame-chd'],
        'chm' => ['application/vnd.ms-htmlhelp', 'application/x-chm'],
        'chrt' => ['application/vnd.kde.kchart', 'application/x-kchart'],
        'cif' => ['chemical/x-cif'],
        'cii' => ['application/vnd.anser-web-certificate-issue-initiation'],
        'cil' => ['application/vnd.ms-artgalry'],
        'cjs' => ['application/node'],
        'cl' => ['text/x-opencl-src'],
        'cla' => ['application/vnd.claymore'],
        'class' => ['application/java', 'application/java-byte-code', 'application/java-vm', 'application/x-java', 'application/x-java-class', 'application/x-java-vm'],
        'cld' => ['model/vnd.cld'],
        'clkk' => ['application/vnd.crick.clicker.keyboard'],
        'clkp' => ['application/vnd.crick.clicker.palette'],
        'clkt' => ['application/vnd.crick.clicker.template'],
        'clkw' => ['application/vnd.crick.clicker.wordbank'],
        'clkx' => ['application/vnd.crick.clicker'],
        'clp' => ['application/x-msclip'],
        'clpi' => ['video/mp2t'],
        'cls' => ['application/x-tex', 'text/x-tex'],
        'cmake' => ['text/x-cmake'],
        'cmc' => ['application/vnd.cosmocaller'],
        'cmdf' => ['chemical/x-cmdf'],
        'cml' => ['chemical/x-cml'],
        'cmp' => ['application/vnd.yellowriver-custom-menu'],
        'cmx' => ['image/x-cmx'],
        'cob' => ['text/x-cobol'],
        'cod' => ['application/vnd.rim.cod'],
        'coffee' => ['application/vnd.coffeescript', 'text/coffeescript'],
        'com' => ['application/x-msdownload'],
        'conf' => ['text/plain'],
        'cpi' => ['video/mp2t'],
        'cpio' => ['application/x-cpio'],
        'cpio.gz' => ['application/x-cpio-compressed'],
        'cpl' => ['application/cpl+xml', 'application/vnd.microsoft.portable-executable', 'application/x-ms-dos-executable', 'application/x-ms-ne-executable', 'application/x-msdownload'],
        'cpp' => ['text/x-c', 'text/x-c++src'],
        'cpt' => ['application/mac-compactpro'],
        'cr' => ['text/crystal', 'text/x-crystal'],
        'cr2' => ['image/x-canon-cr2'],
        'cr3' => ['image/x-canon-cr3'],
        'crd' => ['application/x-mscardfile'],
        'crdownload' => ['application/x-partial-download'],
        'crl' => ['application/pkix-crl'],
        'crt' => ['application/x-x509-ca-cert'],
        'crw' => ['image/x-canon-crw'],
        'crx' => ['application/x-chrome-extension'],
        'cryptonote' => ['application/vnd.rig.cryptonote'],
        'cs' => ['text/x-csharp'],
        'csh' => ['application/x-csh'],
        'csl' => ['application/vnd.citationstyles.style+xml'],
        'csml' => ['chemical/x-csml'],
        'cso' => ['application/x-compressed-iso'],
        'csp' => ['application/vnd.commonspace'],
        'css' => ['text/css'],
        'cst' => ['application/x-director'],
        'csv' => ['text/csv', 'application/csv', 'text/x-comma-separated-values', 'text/x-csv'],
        'csvs' => ['text/csv-schema'],
        'cu' => ['application/cu-seeme'],
        'cue' => ['application/x-cue'],
        'cur' => ['image/x-win-bitmap'],
        'curl' => ['text/vnd.curl'],
        'cwk' => ['application/x-appleworks-document'],
        'cwl' => ['application/cwl'],
        'cww' => ['application/prs.cww'],
        'cxt' => ['application/x-director'],
        'cxx' => ['text/x-c', 'text/x-c++src'],
        'd' => ['text/x-dsrc'],
        'dae' => ['model/vnd.collada+xml'],
        'daf' => ['application/vnd.mobius.daf'],
        'dar' => ['application/x-dar'],
        'dart' => ['application/vnd.dart', 'text/x-dart'],
        'dataless' => ['application/vnd.fdsn.seed'],
        'davmount' => ['application/davmount+xml'],
        'dbf' => ['application/dbase', 'application/dbf', 'application/vnd.dbf', 'application/x-dbase', 'application/x-dbf'],
        'dbk' => ['application/docbook+xml', 'application/vnd.oasis.docbook+xml', 'application/x-docbook+xml'],
        'dc' => ['application/x-dc-rom'],
        'dcl' => ['text/x-dcl'],
        'dcm' => ['application/dicom'],
        'dcr' => ['application/x-director', 'image/x-kodak-dcr'],
        'dcurl' => ['text/vnd.curl.dcurl'],
        'dd2' => ['application/vnd.oma.dd2+xml'],
        'ddd' => ['application/vnd.fujixerox.ddd'],
        'ddf' => ['application/vnd.syncml.dmddf+xml'],
        'dds' => ['image/vnd.ms-dds', 'image/x-dds'],
        'deb' => ['application/vnd.debian.binary-package', 'application/x-deb', 'application/x-debian-package'],
        'def' => ['text/plain'],
        'der' => ['application/x-x509-ca-cert'],
        'desktop' => ['application/x-desktop', 'application/x-gnome-app-info'],
        'device' => ['text/x-systemd-unit'],
        'dfac' => ['application/vnd.dreamfactory'],
        'dff' => ['audio/dff', 'audio/x-dff'],
        'dgc' => ['application/x-dgc-compressed'],
        'di' => ['text/x-dsrc'],
        'dia' => ['application/x-dia-diagram'],
        'dib' => ['image/bmp', 'image/x-bmp', 'image/x-ms-bmp'],
        'dic' => ['text/x-c'],
        'diff' => ['text/x-diff', 'text/x-patch'],
        'dir' => ['application/x-director'],
        'dis' => ['application/vnd.mobius.dis'],
        'disposition-notification' => ['message/disposition-notification'],
        'divx' => ['video/avi', 'video/divx', 'video/msvideo', 'video/vnd.avi', 'video/vnd.divx', 'video/x-avi', 'video/x-msvideo'],
        'djv' => ['image/vnd.djvu', 'image/vnd.djvu+multipage', 'image/x-djvu', 'image/x.djvu'],
        'djvu' => ['image/vnd.djvu', 'image/vnd.djvu+multipage', 'image/x-djvu', 'image/x.djvu'],
        'dll' => ['application/vnd.microsoft.portable-executable', 'application/x-ms-dos-executable', 'application/x-ms-ne-executable', 'application/x-msdownload'],
        'dmg' => ['application/x-apple-diskimage'],
        'dmp' => ['application/pcap', 'application/vnd.tcpdump.pcap', 'application/x-pcap'],
        'dna' => ['application/vnd.dna'],
        'dng' => ['image/x-adobe-dng'],
        'doc' => ['application/msword', 'application/vnd.ms-word', 'application/x-msword', 'zz-application/zz-winassoc-doc'],
        'docbook' => ['application/docbook+xml', 'application/vnd.oasis.docbook+xml', 'application/x-docbook+xml'],
        'docm' => ['application/vnd.ms-word.document.macroenabled.12'],
        'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        'dot' => ['application/msword', 'application/msword-template', 'text/vnd.graphviz'],
        'dotm' => ['application/vnd.ms-word.template.macroenabled.12'],
        'dotx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.template'],
        'dp' => ['application/vnd.osgi.dp'],
        'dpg' => ['application/vnd.dpgraph'],
        'dpx' => ['image/dpx'],
        'dra' => ['audio/vnd.dra'],
        'drl' => ['application/x-excellon'],
        'drle' => ['image/dicom-rle'],
        'drv' => ['application/vnd.microsoft.portable-executable', 'application/x-ms-dos-executable', 'application/x-ms-ne-executable', 'application/x-msdownload'],
        'dsc' => ['text/prs.lines.tag'],
        'dsf' => ['audio/dsd', 'audio/dsf', 'audio/x-dsd', 'audio/x-dsf'],
        'dsl' => ['text/x-dsl'],
        'dssc' => ['application/dssc+der'],
        'dtb' => ['application/x-dtbook+xml', 'text/x-devicetree-binary'],
        'dtd' => ['application/xml-dtd', 'text/x-dtd'],
        'dts' => ['audio/vnd.dts', 'audio/x-dts', 'text/x-devicetree-source'],
        'dtshd' => ['audio/vnd.dts.hd', 'audio/x-dtshd'],
        'dtsi' => ['text/x-devicetree-source'],
        'dtx' => ['application/x-tex', 'text/x-tex'],
        'dv' => ['video/dv'],
        'dvb' => ['video/vnd.dvb.file'],
        'dvi' => ['application/x-dvi'],
        'dvi.bz2' => ['application/x-bzdvi'],
        'dvi.gz' => ['application/x-gzdvi'],
        'dwd' => ['application/atsc-dwd+xml'],
        'dwf' => ['model/vnd.dwf'],
        'dwg' => ['image/vnd.dwg'],
        'dxf' => ['image/vnd.dxf'],
        'dxp' => ['application/vnd.spotfire.dxp'],
        'dxr' => ['application/x-director'],
        'e' => ['text/x-eiffel'],
        'ear' => ['application/java-archive'],
        'ecelp4800' => ['audio/vnd.nuera.ecelp4800'],
        'ecelp7470' => ['audio/vnd.nuera.ecelp7470'],
        'ecelp9600' => ['audio/vnd.nuera.ecelp9600'],
        'ecma' => ['application/ecmascript'],
        'edm' => ['application/vnd.novadigm.edm'],
        'edx' => ['application/vnd.novadigm.edx'],
        'efi' => ['application/vnd.microsoft.portable-executable'],
        'efif' => ['application/vnd.picsel'],
        'egon' => ['application/x-egon'],
        'ei6' => ['application/vnd.pg.osasli'],
        'eif' => ['text/x-eiffel'],
        'el' => ['text/x-emacs-lisp'],
        'emf' => ['application/emf', 'application/x-emf', 'application/x-msmetafile', 'image/emf', 'image/x-emf'],
        'eml' => ['message/rfc822'],
        'emma' => ['application/emma+xml'],
        'emotionml' => ['application/emotionml+xml'],
        'emp' => ['application/vnd.emusic-emusic_package'],
        'emz' => ['application/x-msmetafile'],
        'ent' => ['application/xml-external-parsed-entity', 'text/xml-external-parsed-entity'],
        'eol' => ['audio/vnd.digital-winds'],
        'eot' => ['application/vnd.ms-fontobject'],
        'eps' => ['application/postscript', 'image/x-eps'],
        'eps.bz2' => ['image/x-bzeps'],
        'eps.gz' => ['image/x-gzeps'],
        'epsf' => ['image/x-eps'],
        'epsf.bz2' => ['image/x-bzeps'],
        'epsf.gz' => ['image/x-gzeps'],
        'epsi' => ['image/x-eps'],
        'epsi.bz2' => ['image/x-bzeps'],
        'epsi.gz' => ['image/x-gzeps'],
        'epub' => ['application/epub+zip'],
        'eris' => ['application/x-eris-link+cbor'],
        'erl' => ['text/x-erlang'],
        'es' => ['application/ecmascript', 'text/ecmascript'],
        'es3' => ['application/vnd.eszigno3+xml'],
        'esa' => ['application/vnd.osgi.subsystem'],
        'escn' => ['application/x-godot-scene'],
        'esf' => ['application/vnd.epson.esf'],
        'et3' => ['application/vnd.eszigno3+xml'],
        'etheme' => ['application/x-e-theme'],
        'etx' => ['text/x-setext'],
        'eva' => ['application/x-eva'],
        'evy' => ['application/x-envoy'],
        'ex' => ['text/x-elixir'],
        'exe' => ['application/vnd.microsoft.portable-executable', 'application/x-dosexec', 'application/x-ms-dos-executable', 'application/x-ms-ne-executable', 'application/x-msdos-program', 'application/x-msdownload'],
        'exi' => ['application/exi'],
        'exp' => ['application/express'],
        'exr' => ['image/aces', 'image/x-exr'],
        'exs' => ['text/x-elixir'],
        'ext' => ['application/vnd.novadigm.ext'],
        'ez' => ['application/andrew-inset'],
        'ez2' => ['application/vnd.ezpix-album'],
        'ez3' => ['application/vnd.ezpix-package'],
        'f' => ['text/x-fortran'],
        'f4a' => ['audio/m4a', 'audio/mp4', 'audio/x-m4a'],
        'f4b' => ['audio/x-m4b'],
        'f4v' => ['video/mp4', 'video/mp4v-es', 'video/x-f4v', 'video/x-m4v'],
        'f77' => ['text/x-fortran'],
        'f90' => ['text/x-fortran'],
        'f95' => ['text/x-fortran'],
        'fasl' => ['text/x-common-lisp'],
        'fb2' => ['application/x-fictionbook', 'application/x-fictionbook+xml'],
        'fb2.zip' => ['application/x-zip-compressed-fb2'],
        'fbs' => ['image/vnd.fastbidsheet'],
        'fcdt' => ['application/vnd.adobe.formscentral.fcdt'],
        'fcs' => ['application/vnd.isac.fcs'],
        'fd' => ['application/x-fd-file', 'application/x-raw-floppy-disk-image'],
        'fdf' => ['application/fdf', 'application/vnd.fdf'],
        'fds' => ['application/x-fds-disk'],
        'fdt' => ['application/fdt+xml'],
        'fe_launch' => ['application/vnd.denovo.fcselayout-link'],
        'feature' => ['text/x-gherkin'],
        'fg5' => ['application/vnd.fujitsu.oasysgp'],
        'fgd' => ['application/x-director'],
        'fh' => ['image/x-freehand'],
        'fh4' => ['image/x-freehand'],
        'fh5' => ['image/x-freehand'],
        'fh7' => ['image/x-freehand'],
        'fhc' => ['image/x-freehand'],
        'fig' => ['application/x-xfig', 'image/x-xfig'],
        'fish' => ['application/x-fishscript', 'text/x-fish'],
        'fit' => ['application/fits', 'image/fits', 'image/x-fits'],
        'fits' => ['application/fits', 'image/fits', 'image/x-fits'],
        'fl' => ['application/x-fluid'],
        'flac' => ['audio/flac', 'audio/x-flac'],
        'flatpak' => ['application/vnd.flatpak', 'application/vnd.xdgapp'],
        'flatpakref' => ['application/vnd.flatpak.ref'],
        'flatpakrepo' => ['application/vnd.flatpak.repo'],
        'flc' => ['video/fli', 'video/x-fli', 'video/x-flic'],
        'fli' => ['video/fli', 'video/x-fli', 'video/x-flic'],
        'flo' => ['application/vnd.micrografx.flo'],
        'flv' => ['video/x-flv', 'application/x-flash-video', 'flv-application/octet-stream', 'video/flv'],
        'flw' => ['application/vnd.kde.kivio', 'application/x-kivio'],
        'flx' => ['text/vnd.fmi.flexstor'],
        'fly' => ['text/vnd.fly'],
        'fm' => ['application/vnd.framemaker', 'application/x-frame'],
        'fnc' => ['application/vnd.frogans.fnc'],
        'fo' => ['application/vnd.software602.filler.form+xml', 'text/x-xslfo'],
        'fodg' => ['application/vnd.oasis.opendocument.graphics-flat-xml'],
        'fodp' => ['application/vnd.oasis.opendocument.presentation-flat-xml'],
        'fods' => ['application/vnd.oasis.opendocument.spreadsheet-flat-xml'],
        'fodt' => ['application/vnd.oasis.opendocument.text-flat-xml'],
        'for' => ['text/x-fortran'],
        'fpx' => ['image/vnd.fpx', 'image/x-fpx'],
        'frame' => ['application/vnd.framemaker'],
        'fsc' => ['application/vnd.fsc.weblaunch'],
        'fst' => ['image/vnd.fst'],
        'ftc' => ['application/vnd.fluxtime.clip'],
        'fti' => ['application/vnd.anser-web-funds-transfer-initiation'],
        'fts' => ['application/fits', 'image/fits', 'image/x-fits'],
        'fvt' => ['video/vnd.fvt'],
        'fxm' => ['video/x-javafx'],
        'fxp' => ['application/vnd.adobe.fxp'],
        'fxpl' => ['application/vnd.adobe.fxp'],
        'fzs' => ['application/vnd.fuzzysheet'],
        'g2w' => ['application/vnd.geoplan'],
        'g3' => ['image/fax-g3', 'image/g3fax'],
        'g3w' => ['application/vnd.geospace'],
        'gac' => ['application/vnd.groove-account'],
        'gam' => ['application/x-tads'],
        'gb' => ['application/x-gameboy-rom'],
        'gba' => ['application/x-gba-rom'],
        'gbc' => ['application/x-gameboy-color-rom'],
        'gbr' => ['application/rpki-ghostbusters', 'application/vnd.gerber', 'application/x-gerber', 'image/x-gimp-gbr'],
        'gbrjob' => ['application/x-gerber-job'],
        'gca' => ['application/x-gca-compressed'],
        'gcode' => ['text/x.gcode'],
        'gcrd' => ['text/directory', 'text/vcard', 'text/x-vcard'],
        'gd' => ['application/x-gdscript'],
        'gdi' => ['application/x-gd-rom-cue'],
        'gdl' => ['model/vnd.gdl'],
        'gdoc' => ['application/vnd.google-apps.document'],
        'gdshader' => ['application/x-godot-shader'],
        'ged' => ['application/x-gedcom', 'text/gedcom', 'text/vnd.familysearch.gedcom'],
        'gedcom' => ['application/x-gedcom', 'text/gedcom', 'text/vnd.familysearch.gedcom'],
        'gem' => ['application/x-gtar', 'application/x-tar'],
        'gen' => ['application/x-genesis-rom'],
        'geo' => ['application/vnd.dynageo'],
        'geo.json' => ['application/geo+json', 'application/vnd.geo+json'],
        'geojson' => ['application/geo+json', 'application/vnd.geo+json'],
        'gex' => ['application/vnd.geometry-explorer'],
        'gf' => ['application/x-tex-gf'],
        'gg' => ['application/x-gamegear-rom'],
        'ggb' => ['application/vnd.geogebra.file'],
        'ggs' => ['application/vnd.geogebra.slides'],
        'ggt' => ['application/vnd.geogebra.tool'],
        'ghf' => ['application/vnd.groove-help'],
        'gif' => ['image/gif'],
        'gih' => ['image/x-gimp-gih'],
        'gim' => ['application/vnd.groove-identity-message'],
        'glade' => ['application/x-glade'],
        'glb' => ['model/gltf-binary'],
        'gltf' => ['model/gltf+json'],
        'gml' => ['application/gml+xml'],
        'gmo' => ['application/x-gettext-translation'],
        'gmx' => ['application/vnd.gmx'],
        'gnc' => ['application/x-gnucash'],
        'gnd' => ['application/gnunet-directory'],
        'gnucash' => ['application/x-gnucash'],
        'gnumeric' => ['application/x-gnumeric'],
        'gnuplot' => ['application/x-gnuplot'],
        'go' => ['text/x-go'],
        'gp' => ['application/x-gnuplot'],
        'gpg' => ['application/pgp', 'application/pgp-encrypted', 'application/pgp-keys', 'application/pgp-signature'],
        'gph' => ['application/vnd.flographit'],
        'gplt' => ['application/x-gnuplot'],
        'gpx' => ['application/gpx', 'application/gpx+xml', 'application/x-gpx', 'application/x-gpx+xml'],
        'gqf' => ['application/vnd.grafeq'],
        'gqs' => ['application/vnd.grafeq'],
        'gra' => ['application/x-graphite'],
        'gradle' => ['text/x-gradle'],
        'gram' => ['application/srgs'],
        'gramps' => ['application/x-gramps-xml'],
        'gre' => ['application/vnd.geometry-explorer'],
        'groovy' => ['text/x-groovy'],
        'grv' => ['application/vnd.groove-injector'],
        'grxml' => ['application/srgs+xml'],
        'gs' => ['text/x-genie'],
        'gsf' => ['application/x-font-ghostscript', 'application/x-font-type1'],
        'gsh' => ['text/x-groovy'],
        'gsheet' => ['application/vnd.google-apps.spreadsheet'],
        'gslides' => ['application/vnd.google-apps.presentation'],
        'gsm' => ['audio/x-gsm'],
        'gtar' => ['application/x-gtar', 'application/x-tar'],
        'gtm' => ['application/vnd.groove-tool-message'],
        'gtw' => ['model/vnd.gtw'],
        'gv' => ['text/vnd.graphviz'],
        'gvp' => ['text/google-video-pointer', 'text/x-google-video-pointer'],
        'gvy' => ['text/x-groovy'],
        'gx' => ['text/x-gcode-gx'],
        'gxf' => ['application/gxf'],
        'gxt' => ['application/vnd.geonext'],
        'gy' => ['text/x-groovy'],
        'gz' => ['application/x-gzip', 'application/gzip'],
        'h' => ['text/x-c', 'text/x-chdr'],
        'h++' => ['text/x-c++hdr'],
        'h261' => ['video/h261'],
        'h263' => ['video/h263'],
        'h264' => ['video/h264'],
        'h4' => ['application/x-hdf'],
        'h5' => ['application/x-hdf'],
        'hal' => ['application/vnd.hal+xml'],
        'hbci' => ['application/vnd.hbci'],
        'hbs' => ['text/x-handlebars-template'],
        'hdd' => ['application/x-virtualbox-hdd'],
        'hdf' => ['application/x-hdf'],
        'hdf4' => ['application/x-hdf'],
        'hdf5' => ['application/x-hdf'],
        'hdp' => ['image/jxr', 'image/vnd.ms-photo'],
        'heic' => ['image/heic', 'image/heic-sequence', 'image/heif', 'image/heif-sequence'],
        'heics' => ['image/heic-sequence'],
        'heif' => ['image/heic', 'image/heic-sequence', 'image/heif', 'image/heif-sequence'],
        'heifs' => ['image/heif-sequence'],
        'hej2' => ['image/hej2k'],
        'held' => ['application/atsc-held+xml'],
        'hfe' => ['application/x-hfe-file', 'application/x-hfe-floppy-image'],
        'hh' => ['text/x-c', 'text/x-c++hdr'],
        'hif' => ['image/heic', 'image/heic-sequence', 'image/heif', 'image/heif-sequence'],
        'hjson' => ['application/hjson'],
        'hlp' => ['application/winhlp', 'zz-application/zz-winassoc-hlp'],
        'hp' => ['text/x-c++hdr'],
        'hpgl' => ['application/vnd.hp-hpgl'],
        'hpid' => ['application/vnd.hp-hpid'],
        'hpp' => ['text/x-c++hdr'],
        'hps' => ['application/vnd.hp-hps'],
        'hqx' => ['application/stuffit', 'application/mac-binhex40'],
        'hs' => ['text/x-haskell'],
        'hsj2' => ['image/hsj2'],
        'hta' => ['application/hta'],
        'htc' => ['text/x-component'],
        'htke' => ['application/vnd.kenameaapp'],
        'htm' => ['text/html', 'application/xhtml+xml'],
        'html' => ['text/html', 'application/xhtml+xml'],
        'hvd' => ['application/vnd.yamaha.hv-dic'],
        'hvp' => ['application/vnd.yamaha.hv-voice'],
        'hvs' => ['application/vnd.yamaha.hv-script'],
        'hwp' => ['application/vnd.haansoft-hwp', 'application/x-hwp'],
        'hwt' => ['application/vnd.haansoft-hwt', 'application/x-hwt'],
        'hxx' => ['text/x-c++hdr'],
        'i2g' => ['application/vnd.intergeo'],
        'ica' => ['application/x-ica'],
        'icalendar' => ['application/ics', 'text/calendar', 'text/x-vcalendar'],
        'icb' => ['application/tga', 'application/x-targa', 'application/x-tga', 'image/targa', 'image/tga', 'image/x-icb', 'image/x-targa', 'image/x-tga'],
        'icc' => ['application/vnd.iccprofile'],
        'ice' => ['x-conference/x-cooltalk'],
        'icm' => ['application/vnd.iccprofile'],
        'icns' => ['image/x-icns'],
        'ico' => ['application/ico', 'image/ico', 'image/icon', 'image/vnd.microsoft.icon', 'image/x-ico', 'image/x-icon', 'text/ico'],
        'ics' => ['application/ics', 'text/calendar', 'text/x-vcalendar'],
        'idl' => ['text/x-idl'],
        'ief' => ['image/ief'],
        'ifb' => ['application/ics', 'text/calendar', 'text/x-vcalendar'],
        'iff' => ['image/x-iff', 'image/x-ilbm'],
        'ifm' => ['application/vnd.shana.informed.formdata'],
        'iges' => ['model/iges'],
        'igl' => ['application/vnd.igloader'],
        'igm' => ['application/vnd.insors.igm'],
        'igs' => ['model/iges'],
        'igx' => ['application/vnd.micrografx.igx'],
        'iif' => ['application/vnd.shana.informed.interchange'],
        'ilbm' => ['image/x-iff', 'image/x-ilbm'],
        'ime' => ['audio/imelody', 'audio/x-imelody', 'text/x-imelody'],
        'img' => ['application/vnd.efi.img', 'application/x-raw-disk-image'],
        'img.xz' => ['application/x-raw-disk-image-xz-compressed'],
        'imp' => ['application/vnd.accpac.simply.imp'],
        'ims' => ['application/vnd.ms-ims'],
        'imy' => ['audio/imelody', 'audio/x-imelody', 'text/x-imelody'],
        'in' => ['text/plain'],
        'ini' => ['text/plain'],
        'ink' => ['application/inkml+xml'],
        'inkml' => ['application/inkml+xml'],
        'ins' => ['application/x-tex', 'text/x-tex'],
        'install' => ['application/x-install-instructions'],
        'iota' => ['application/vnd.astraea-software.iota'],
        'ipfix' => ['application/ipfix'],
        'ipk' => ['application/vnd.shana.informed.package'],
        'ips' => ['application/x-ips-patch'],
        'iptables' => ['text/x-iptables'],
        'ipynb' => ['application/x-ipynb+json'],
        'irm' => ['application/vnd.ibm.rights-management'],
        'irp' => ['application/vnd.irepository.package+xml'],
        'iso' => ['application/vnd.efi.iso', 'application/x-cd-image', 'application/x-dreamcast-rom', 'application/x-gamecube-iso-image', 'application/x-gamecube-rom', 'application/x-iso9660-image', 'application/x-saturn-rom', 'application/x-sega-cd-rom', 'application/x-sega-pico-rom', 'application/x-wbfs', 'application/x-wia', 'application/x-wii-iso-image', 'application/x-wii-rom'],
        'iso9660' => ['application/vnd.efi.iso', 'application/x-cd-image', 'application/x-iso9660-image'],
        'it' => ['audio/x-it'],
        'it87' => ['application/x-it87'],
        'itp' => ['application/vnd.shana.informed.formtemplate'],
        'its' => ['application/its+xml'],
        'ivp' => ['application/vnd.immervision-ivp'],
        'ivu' => ['application/vnd.immervision-ivu'],
        'j2c' => ['image/x-jp2-codestream'],
        'j2k' => ['image/x-jp2-codestream'],
        'jad' => ['text/vnd.sun.j2me.app-descriptor'],
        'jade' => ['text/jade'],
        'jam' => ['application/vnd.jam'],
        'jar' => ['application/x-java-archive', 'application/java-archive', 'application/x-jar'],
        'jardiff' => ['application/x-java-archive-diff'],
        'java' => ['text/x-java', 'text/x-java-source'],
        'jceks' => ['application/x-java-jce-keystore'],
        'jfif' => ['image/jpeg', 'image/pjpeg'],
        'jhc' => ['image/jphc'],
        'jisp' => ['application/vnd.jisp'],
        'jks' => ['application/x-java-keystore'],
        'jl' => ['text/julia'],
        'jls' => ['image/jls'],
        'jlt' => ['application/vnd.hp-jlyt'],
        'jng' => ['image/x-jng'],
        'jnlp' => ['application/x-java-jnlp-file'],
        'joda' => ['application/vnd.joost.joda-archive'],
        'jp2' => ['image/jp2', 'image/jpeg2000', 'image/jpeg2000-image', 'image/x-jpeg2000-image'],
        'jpc' => ['image/x-jp2-codestream'],
        'jpe' => ['image/jpeg', 'image/pjpeg'],
        'jpeg' => ['image/jpeg', 'image/pjpeg'],
        'jpf' => ['image/jpx'],
        'jpg' => ['image/jpeg', 'image/pjpeg'],
        'jpg2' => ['image/jp2', 'image/jpeg2000', 'image/jpeg2000-image', 'image/x-jpeg2000-image'],
        'jpgm' => ['image/jpm', 'video/jpm'],
        'jpgv' => ['video/jpeg'],
        'jph' => ['image/jph'],
        'jpm' => ['image/jpm', 'video/jpm'],
        'jpr' => ['application/x-jbuilder-project'],
        'jpx' => ['application/x-jbuilder-project', 'image/jpx'],
        'jrd' => ['application/jrd+json'],
        'js' => ['text/javascript', 'application/javascript', 'application/x-javascript', 'text/jscript'],
        'jse' => ['text/jscript.encode'],
        'jsm' => ['application/javascript', 'application/x-javascript', 'text/javascript', 'text/jscript'],
        'json' => ['application/json', 'application/schema+json'],
        'json-patch' => ['application/json-patch+json'],
        'json5' => ['application/json5'],
        'jsonld' => ['application/ld+json'],
        'jsonml' => ['application/jsonml+json'],
        'jsx' => ['text/jsx'],
        'jt' => ['model/jt'],
        'jxl' => ['image/jxl'],
        'jxr' => ['image/jxr', 'image/vnd.ms-photo'],
        'jxra' => ['image/jxra'],
        'jxrs' => ['image/jxrs'],
        'jxs' => ['image/jxs'],
        'jxsc' => ['image/jxsc'],
        'jxsi' => ['image/jxsi'],
        'jxss' => ['image/jxss'],
        'k25' => ['image/x-kodak-k25'],
        'k7' => ['application/x-thomson-cassette'],
        'kar' => ['audio/midi', 'audio/x-midi'],
        'karbon' => ['application/vnd.kde.karbon', 'application/x-karbon'],
        'kcf' => ['image/x-kiss-cel'],
        'kdbx' => ['application/x-keepass2'],
        'kdc' => ['image/x-kodak-kdc'],
        'kdelnk' => ['application/x-desktop', 'application/x-gnome-app-info'],
        'kexi' => ['application/x-kexiproject-sqlite', 'application/x-kexiproject-sqlite2', 'application/x-kexiproject-sqlite3', 'application/x-vnd.kde.kexi'],
        'kexic' => ['application/x-kexi-connectiondata'],
        'kexis' => ['application/x-kexiproject-shortcut'],
        'key' => ['application/vnd.apple.keynote', 'application/pgp-keys', 'application/x-iwork-keynote-sffkey'],
        'keynote' => ['application/vnd.apple.keynote'],
        'kfo' => ['application/vnd.kde.kformula', 'application/x-kformula'],
        'kfx' => ['application/vnd.amazon.mobi8-ebook', 'application/x-mobi8-ebook'],
        'kia' => ['application/vnd.kidspiration'],
        'kil' => ['application/x-killustrator'],
        'kino' => ['application/smil', 'application/smil+xml'],
        'kml' => ['application/vnd.google-earth.kml+xml'],
        'kmz' => ['application/vnd.google-earth.kmz'],
        'kne' => ['application/vnd.kinar'],
        'knp' => ['application/vnd.kinar'],
        'kon' => ['application/vnd.kde.kontour', 'application/x-kontour'],
        'kpm' => ['application/x-kpovmodeler'],
        'kpr' => ['application/vnd.kde.kpresenter', 'application/x-kpresenter'],
        'kpt' => ['application/vnd.kde.kpresenter', 'application/x-kpresenter'],
        'kpxx' => ['application/vnd.ds-keypoint'],
        'kra' => ['application/x-krita'],
        'krz' => ['application/x-krita'],
        'ks' => ['application/x-java-keystore'],
        'ksp' => ['application/vnd.kde.kspread', 'application/x-kspread'],
        'ksy' => ['text/x-kaitai-struct'],
        'kt' => ['text/x-kotlin'],
        'ktr' => ['application/vnd.kahootz'],
        'ktx' => ['image/ktx'],
        'ktx2' => ['image/ktx2'],
        'ktz' => ['application/vnd.kahootz'],
        'kud' => ['application/x-kugar'],
        'kwd' => ['application/vnd.kde.kword', 'application/x-kword'],
        'kwt' => ['application/vnd.kde.kword', 'application/x-kword'],
        'la' => ['application/x-shared-library-la'],
        'lasxml' => ['application/vnd.las.las+xml'],
        'latex' => ['application/x-latex', 'application/x-tex', 'text/x-tex'],
        'lbd' => ['application/vnd.llamagraphics.life-balance.desktop'],
        'lbe' => ['application/vnd.llamagraphics.life-balance.exchange+xml'],
        'lbm' => ['image/x-iff', 'image/x-ilbm'],
        'ldif' => ['text/x-ldif'],
        'les' => ['application/vnd.hhe.lesson-player'],
        'less' => ['text/less'],
        'lgr' => ['application/lgr+xml'],
        'lha' => ['application/x-lha', 'application/x-lzh-compressed'],
        'lhs' => ['text/x-literate-haskell'],
        'lhz' => ['application/x-lhz'],
        'lib' => ['application/vnd.microsoft.portable-executable', 'application/x-archive'],
        'link66' => ['application/vnd.route66.link66+xml'],
        'lisp' => ['text/x-common-lisp'],
        'list' => ['text/plain'],
        'list3820' => ['application/vnd.ibm.modcap'],
        'listafp' => ['application/vnd.ibm.modcap'],
        'litcoffee' => ['text/coffeescript'],
        'lmdb' => ['application/x-lmdb'],
        'lnk' => ['application/x-ms-shortcut', 'application/x-win-lnk'],
        'lnx' => ['application/x-atari-lynx-rom'],
        'loas' => ['audio/usac'],
        'log' => ['text/plain', 'text/x-log'],
        'lostxml' => ['application/lost+xml'],
        'lrf' => ['application/x-sony-bbeb'],
        'lrm' => ['application/vnd.ms-lrm'],
        'lrv' => ['video/mp4', 'video/mp4v-es', 'video/x-m4v'],
        'lrz' => ['application/x-lrzip'],
        'ltf' => ['application/vnd.frogans.ltf'],
        'ltx' => ['application/x-tex', 'text/x-tex'],
        'lua' => ['text/x-lua'],
        'luac' => ['application/x-lua-bytecode'],
        'lvp' => ['audio/vnd.lucent.voice'],
        'lwo' => ['image/x-lwo'],
        'lwob' => ['image/x-lwo'],
        'lwp' => ['application/vnd.lotus-wordpro'],
        'lws' => ['image/x-lws'],
        'ly' => ['text/x-lilypond'],
        'lyx' => ['application/x-lyx', 'text/x-lyx'],
        'lz' => ['application/x-lzip'],
        'lz4' => ['application/x-lz4'],
        'lzh' => ['application/x-lha', 'application/x-lzh-compressed'],
        'lzma' => ['application/x-lzma'],
        'lzo' => ['application/x-lzop'],
        'm' => ['text/x-matlab', 'text/x-objcsrc', 'text/x-octave'],
        'm13' => ['application/x-msmediaview'],
        'm14' => ['application/x-msmediaview'],
        'm15' => ['audio/x-mod'],
        'm1u' => ['video/vnd.mpegurl', 'video/x-mpegurl'],
        'm1v' => ['video/mpeg'],
        'm21' => ['application/mp21'],
        'm2a' => ['audio/mpeg'],
        'm2t' => ['video/mp2t'],
        'm2ts' => ['video/mp2t'],
        'm2v' => ['video/mpeg'],
        'm3a' => ['audio/mpeg'],
        'm3u' => ['audio/x-mpegurl', 'application/m3u', 'application/vnd.apple.mpegurl', 'audio/m3u', 'audio/mpegurl', 'audio/x-m3u', 'audio/x-mp3-playlist'],
        'm3u8' => ['application/m3u', 'application/vnd.apple.mpegurl', 'audio/m3u', 'audio/mpegurl', 'audio/x-m3u', 'audio/x-mp3-playlist', 'audio/x-mpegurl'],
        'm4' => ['application/x-m4'],
        'm4a' => ['audio/mp4', 'audio/m4a', 'audio/x-m4a'],
        'm4b' => ['audio/x-m4b'],
        'm4p' => ['application/mp4'],
        'm4r' => ['audio/x-m4r'],
        'm4s' => ['video/iso.segment'],
        'm4u' => ['video/vnd.mpegurl', 'video/x-mpegurl'],
        'm4v' => ['video/mp4', 'video/mp4v-es', 'video/x-m4v'],
        'm7' => ['application/x-thomson-cartridge-memo7'],
        'ma' => ['application/mathematica'],
        'mab' => ['application/x-markaby'],
        'mads' => ['application/mads+xml'],
        'maei' => ['application/mmt-aei+xml'],
        'mag' => ['application/vnd.ecowin.chart'],
        'mak' => ['text/x-makefile'],
        'maker' => ['application/vnd.framemaker'],
        'man' => ['application/x-troff-man', 'text/troff'],
        'manifest' => ['text/cache-manifest'],
        'map' => ['application/json'],
        'markdown' => ['text/markdown', 'text/x-markdown'],
        'mathml' => ['application/mathml+xml'],
        'mb' => ['application/mathematica'],
        'mbk' => ['application/vnd.mobius.mbk'],
        'mbox' => ['application/mbox'],
        'mc1' => ['application/vnd.medcalcdata'],
        'mc2' => ['text/vnd.senx.warpscript'],
        'mcd' => ['application/vnd.mcd'],
        'mcurl' => ['text/vnd.curl.mcurl'],
        'md' => ['text/markdown', 'text/x-markdown', 'application/x-genesis-rom'],
        'mdb' => ['application/x-msaccess', 'application/mdb', 'application/msaccess', 'application/vnd.ms-access', 'application/vnd.msaccess', 'application/x-lmdb', 'application/x-mdb', 'zz-application/zz-winassoc-mdb'],
        'mdi' => ['image/vnd.ms-modi'],
        'mdx' => ['application/x-genesis-32x-rom', 'text/mdx'],
        'me' => ['text/troff', 'text/x-troff-me'],
        'med' => ['audio/x-mod'],
        'mesh' => ['model/mesh'],
        'meta4' => ['application/metalink4+xml'],
        'metalink' => ['application/metalink+xml'],
        'mets' => ['application/mets+xml'],
        'mfm' => ['application/vnd.mfmp'],
        'mft' => ['application/rpki-manifest'],
        'mgp' => ['application/vnd.osgeo.mapguide.package', 'application/x-magicpoint'],
        'mgz' => ['application/vnd.proteus.magazine'],
        'mht' => ['application/x-mimearchive'],
        'mhtml' => ['application/x-mimearchive'],
        'mid' => ['audio/midi', 'audio/x-midi'],
        'midi' => ['audio/midi', 'audio/x-midi'],
        'mie' => ['application/x-mie'],
        'mif' => ['application/vnd.mif', 'application/x-mif'],
        'mime' => ['message/rfc822'],
        'minipsf' => ['audio/x-minipsf'],
        'mj2' => ['video/mj2'],
        'mjp2' => ['video/mj2'],
        'mjpeg' => ['video/x-mjpeg'],
        'mjpg' => ['video/x-mjpeg'],
        'mjs' => ['application/javascript', 'application/x-javascript', 'text/javascript', 'text/jscript'],
        'mk' => ['text/x-makefile'],
        'mk3d' => ['video/x-matroska', 'video/x-matroska-3d'],
        'mka' => ['audio/x-matroska'],
        'mkd' => ['text/markdown', 'text/x-markdown'],
        'mks' => ['video/x-matroska'],
        'mkv' => ['video/x-matroska'],
        'ml' => ['text/x-ocaml'],
        'mli' => ['text/x-ocaml'],
        'mlp' => ['application/vnd.dolby.mlp'],
        'mm' => ['text/x-objc++src', 'text/x-troff-mm'],
        'mmd' => ['application/vnd.chipnuts.karaoke-mmd'],
        'mmf' => ['application/vnd.smaf', 'application/x-smaf'],
        'mml' => ['application/mathml+xml', 'text/mathml'],
        'mmr' => ['image/vnd.fujixerox.edmics-mmr'],
        'mng' => ['video/x-mng'],
        'mny' => ['application/x-msmoney'],
        'mo' => ['application/x-gettext-translation', 'text/x-modelica'],
        'mo3' => ['audio/x-mo3'],
        'mobi' => ['application/x-mobipocket-ebook'],
        'moc' => ['text/x-moc'],
        'mod' => ['application/x-object', 'audio/x-mod'],
        'mods' => ['application/mods+xml'],
        'mof' => ['text/x-mof'],
        'moov' => ['video/quicktime'],
        'mount' => ['text/x-systemd-unit'],
        'mov' => ['video/quicktime'],
        'movie' => ['video/x-sgi-movie'],
        'mp+' => ['audio/x-musepack'],
        'mp2' => ['audio/mp2', 'audio/mpeg', 'audio/x-mp2', 'video/mpeg', 'video/mpeg-system', 'video/x-mpeg', 'video/x-mpeg-system', 'video/x-mpeg2'],
        'mp21' => ['application/mp21'],
        'mp2a' => ['audio/mpeg'],
        'mp3' => ['audio/mpeg', 'audio/mp3', 'audio/x-mp3', 'audio/x-mpeg', 'audio/x-mpg'],
        'mp4' => ['video/mp4', 'application/mp4', 'video/mp4v-es', 'video/x-m4v'],
        'mp4a' => ['audio/mp4'],
        'mp4s' => ['application/mp4'],
        'mp4v' => ['video/mp4'],
        'mpc' => ['application/vnd.mophun.certificate', 'audio/x-musepack'],
        'mpd' => ['application/dash+xml'],
        'mpe' => ['video/mpeg', 'video/mpeg-system', 'video/x-mpeg', 'video/x-mpeg-system', 'video/x-mpeg2'],
        'mpeg' => ['video/mpeg', 'video/mpeg-system', 'video/x-mpeg', 'video/x-mpeg-system', 'video/x-mpeg2'],
        'mpf' => ['application/media-policy-dataset+xml'],
        'mpg' => ['video/mpeg', 'video/mpeg-system', 'video/x-mpeg', 'video/x-mpeg-system', 'video/x-mpeg2'],
        'mpg4' => ['video/mpg4', 'application/mp4', 'video/mp4'],
        'mpga' => ['audio/mp3', 'audio/mpeg', 'audio/x-mp3', 'audio/x-mpeg', 'audio/x-mpg'],
        'mpkg' => ['application/vnd.apple.installer+xml'],
        'mpl' => ['text/x-mpl2', 'video/mp2t'],
        'mpls' => ['video/mp2t'],
        'mpm' => ['application/vnd.blueice.multipass'],
        'mpn' => ['application/vnd.mophun.application'],
        'mpp' => ['application/dash-patch+xml', 'application/vnd.ms-project', 'audio/x-musepack'],
        'mpt' => ['application/vnd.ms-project'],
        'mpy' => ['application/vnd.ibm.minipay'],
        'mqy' => ['application/vnd.mobius.mqy'],
        'mrc' => ['application/marc'],
        'mrcx' => ['application/marcxml+xml'],
        'mrl' => ['text/x-mrml'],
        'mrml' => ['text/x-mrml'],
        'mrpack' => ['application/x-modrinth-modpack+zip'],
        'mrw' => ['image/x-minolta-mrw'],
        'ms' => ['text/troff', 'text/x-troff-ms'],
        'mscml' => ['application/mediaservercontrol+xml'],
        'mseed' => ['application/vnd.fdsn.mseed'],
        'mseq' => ['application/vnd.mseq'],
        'msf' => ['application/vnd.epson.msf'],
        'msg' => ['application/vnd.ms-outlook'],
        'msh' => ['model/mesh'],
        'msi' => ['application/x-msdownload', 'application/x-msi'],
        'msix' => ['application/msix'],
        'msixbundle' => ['application/msixbundle'],
        'msl' => ['application/vnd.mobius.msl'],
        'msod' => ['image/x-msod'],
        'msp' => ['application/microsoftpatch'],
        'msty' => ['application/vnd.muvee.style'],
        'msu' => ['application/microsoftupdate'],
        'msx' => ['application/x-msx-rom'],
        'mtl' => ['model/mtl'],
        'mtm' => ['audio/x-mod'],
        'mts' => ['model/vnd.mts', 'video/mp2t'],
        'mup' => ['text/x-mup'],
        'mus' => ['application/vnd.musician'],
        'musd' => ['application/mmt-usd+xml'],
        'musicxml' => ['application/vnd.recordare.musicxml+xml'],
        'mvb' => ['application/x-msmediaview'],
        'mvt' => ['application/vnd.mapbox-vector-tile'],
        'mwf' => ['application/vnd.mfer'],
        'mxf' => ['application/mxf'],
        'mxl' => ['application/vnd.recordare.musicxml'],
        'mxmf' => ['audio/mobile-xmf', 'audio/vnd.nokia.mobile-xmf'],
        'mxml' => ['application/xv+xml'],
        'mxs' => ['application/vnd.triscape.mxs'],
        'mxu' => ['video/vnd.mpegurl', 'video/x-mpegurl'],
        'n-gage' => ['application/vnd.nokia.n-gage.symbian.install'],
        'n3' => ['text/n3'],
        'n64' => ['application/x-n64-rom'],
        'nb' => ['application/mathematica', 'application/x-mathematica'],
        'nbp' => ['application/vnd.wolfram.player'],
        'nc' => ['application/x-netcdf'],
        'ncx' => ['application/x-dtbncx+xml'],
        'nds' => ['application/x-nintendo-ds-rom'],
        'nef' => ['image/x-nikon-nef'],
        'nes' => ['application/x-nes-rom'],
        'nez' => ['application/x-nes-rom'],
        'nfo' => ['text/x-nfo'],
        'ngc' => ['application/x-neo-geo-pocket-color-rom'],
        'ngdat' => ['application/vnd.nokia.n-gage.data'],
        'ngp' => ['application/x-neo-geo-pocket-rom'],
        'nim' => ['text/x-nim'],
        'nimble' => ['text/x-nimscript'],
        'nims' => ['text/x-nimscript'],
        'nitf' => ['application/vnd.nitf'],
        'nix' => ['text/x-nix'],
        'nlu' => ['application/vnd.neurolanguage.nlu'],
        'nml' => ['application/vnd.enliven'],
        'nnd' => ['application/vnd.noblenet-directory'],
        'nns' => ['application/vnd.noblenet-sealer'],
        'nnw' => ['application/vnd.noblenet-web'],
        'not' => ['text/x-mup'],
        'npx' => ['image/vnd.net-fpx'],
        'nq' => ['application/n-quads'],
        'nrw' => ['image/x-nikon-nrw'],
        'nsc' => ['application/x-conference', 'application/x-netshow-channel'],
        'nsf' => ['application/vnd.lotus-notes'],
        'nsv' => ['video/x-nsv'],
        'nt' => ['application/n-triples'],
        'ntar' => ['application/x-pcapng'],
        'ntf' => ['application/vnd.nitf'],
        'nu' => ['application/x-nuscript', 'text/x-nu'],
        'numbers' => ['application/vnd.apple.numbers', 'application/x-iwork-numbers-sffnumbers'],
        'nzb' => ['application/x-nzb'],
        'o' => ['application/x-object'],
        'oa2' => ['application/vnd.fujitsu.oasys2'],
        'oa3' => ['application/vnd.fujitsu.oasys3'],
        'oas' => ['application/vnd.fujitsu.oasys'],
        'obd' => ['application/x-msbinder'],
        'obgx' => ['application/vnd.openblox.game+xml'],
        'obj' => ['application/prs.wavefront-obj', 'application/x-tgif', 'model/obj'],
        'ocl' => ['text/x-ocl'],
        'ocx' => ['application/vnd.microsoft.portable-executable'],
        'oda' => ['application/oda'],
        'odb' => ['application/vnd.oasis.opendocument.base', 'application/vnd.oasis.opendocument.database', 'application/vnd.sun.xml.base'],
        'odc' => ['application/vnd.oasis.opendocument.chart'],
        'odf' => ['application/vnd.oasis.opendocument.formula'],
        'odft' => ['application/vnd.oasis.opendocument.formula-template'],
        'odg' => ['application/vnd.oasis.opendocument.graphics'],
        'odi' => ['application/vnd.oasis.opendocument.image'],
        'odm' => ['application/vnd.oasis.opendocument.text-master'],
        'odp' => ['application/vnd.oasis.opendocument.presentation'],
        'ods' => ['application/vnd.oasis.opendocument.spreadsheet'],
        'odt' => ['application/vnd.oasis.opendocument.text'],
        'oga' => ['audio/ogg', 'audio/vorbis', 'audio/x-flac+ogg', 'audio/x-ogg', 'audio/x-oggflac', 'audio/x-speex+ogg', 'audio/x-vorbis', 'audio/x-vorbis+ogg'],
        'ogex' => ['model/vnd.opengex'],
        'ogg' => ['audio/ogg', 'audio/vorbis', 'audio/x-flac+ogg', 'audio/x-ogg', 'audio/x-oggflac', 'audio/x-speex+ogg', 'audio/x-vorbis', 'audio/x-vorbis+ogg', 'video/ogg', 'video/x-ogg', 'video/x-theora', 'video/x-theora+ogg'],
        'ogm' => ['video/x-ogm', 'video/x-ogm+ogg'],
        'ogv' => ['video/ogg', 'video/x-ogg'],
        'ogx' => ['application/ogg', 'application/x-ogg'],
        'old' => ['application/x-trash'],
        'oleo' => ['application/x-oleo'],
        'omdoc' => ['application/omdoc+xml'],
        'onepkg' => ['application/onenote'],
        'onetmp' => ['application/onenote'],
        'onetoc' => ['application/onenote'],
        'onetoc2' => ['application/onenote'],
        'ooc' => ['text/x-ooc'],
        'openvpn' => ['application/x-openvpn-profile'],
        'opf' => ['application/oebps-package+xml'],
        'opml' => ['text/x-opml', 'text/x-opml+xml'],
        'oprc' => ['application/vnd.palm', 'application/x-palm-database'],
        'opus' => ['audio/ogg', 'audio/x-ogg', 'audio/x-opus+ogg'],
        'ora' => ['image/openraster'],
        'orf' => ['image/x-olympus-orf'],
        'org' => ['application/vnd.lotus-organizer', 'text/org', 'text/x-org'],
        'osf' => ['application/vnd.yamaha.openscoreformat'],
        'osfpvg' => ['application/vnd.yamaha.openscoreformat.osfpvg+xml'],
        'osm' => ['application/vnd.openstreetmap.data+xml'],
        'otc' => ['application/vnd.oasis.opendocument.chart-template'],
        'otf' => ['application/vnd.oasis.opendocument.formula-template', 'application/x-font-otf', 'font/otf'],
        'otg' => ['application/vnd.oasis.opendocument.graphics-template'],
        'oth' => ['application/vnd.oasis.opendocument.text-web'],
        'oti' => ['application/vnd.oasis.opendocument.image-template'],
        'otm' => ['application/vnd.oasis.opendocument.text-master-template'],
        'otp' => ['application/vnd.oasis.opendocument.presentation-template'],
        'ots' => ['application/vnd.oasis.opendocument.spreadsheet-template'],
        'ott' => ['application/vnd.oasis.opendocument.text-template'],
        'ova' => ['application/ovf', 'application/x-virtualbox-ova'],
        'ovf' => ['application/x-virtualbox-ovf'],
        'ovpn' => ['application/x-openvpn-profile'],
        'owl' => ['application/rdf+xml', 'text/rdf'],
        'owx' => ['application/owl+xml'],
        'oxps' => ['application/oxps'],
        'oxt' => ['application/vnd.openofficeorg.extension'],
        'p' => ['text/x-pascal'],
        'p10' => ['application/pkcs10'],
        'p12' => ['application/pkcs12', 'application/x-pkcs12'],
        'p65' => ['application/x-pagemaker'],
        'p7b' => ['application/x-pkcs7-certificates'],
        'p7c' => ['application/pkcs7-mime'],
        'p7m' => ['application/pkcs7-mime'],
        'p7r' => ['application/x-pkcs7-certreqresp'],
        'p7s' => ['application/pkcs7-signature'],
        'p8' => ['application/pkcs8'],
        'p8e' => ['application/pkcs8-encrypted'],
        'pac' => ['application/x-ns-proxy-autoconfig'],
        'pack' => ['application/x-java-pack200'],
        'pages' => ['application/vnd.apple.pages', 'application/x-iwork-pages-sffpages'],
        'pak' => ['application/x-pak'],
        'par2' => ['application/x-par2'],
        'parquet' => ['application/vnd.apache.parquet', 'application/x-parquet'],
        'part' => ['application/x-partial-download'],
        'pas' => ['text/x-pascal'],
        'pat' => ['image/x-gimp-pat'],
        'patch' => ['text/x-diff', 'text/x-patch'],
        'path' => ['text/x-systemd-unit'],
        'paw' => ['application/vnd.pawaafile'],
        'pbd' => ['application/vnd.powerbuilder6'],
        'pbm' => ['image/x-portable-bitmap'],
        'pcap' => ['application/pcap', 'application/vnd.tcpdump.pcap', 'application/x-pcap'],
        'pcapng' => ['application/x-pcapng'],
        'pcd' => ['image/x-photo-cd'],
        'pce' => ['application/x-pc-engine-rom'],
        'pcf' => ['application/x-cisco-vpn-settings', 'application/x-font-pcf'],
        'pcf.Z' => ['application/x-font-pcf'],
        'pcf.gz' => ['application/x-font-pcf'],
        'pcl' => ['application/vnd.hp-pcl'],
        'pclxl' => ['application/vnd.hp-pclxl'],
        'pct' => ['image/x-pict'],
        'pcurl' => ['application/vnd.curl.pcurl'],
        'pcx' => ['image/vnd.zbrush.pcx', 'image/x-pcx'],
        'pdb' => ['application/vnd.palm', 'application/x-aportisdoc', 'application/x-ms-pdb', 'application/x-palm-database', 'application/x-pilot', 'chemical/x-pdb'],
        'pdc' => ['application/x-aportisdoc'],
        'pde' => ['text/x-processing'],
        'pdf' => ['application/pdf', 'application/acrobat', 'application/nappdf', 'application/x-pdf', 'image/pdf'],
        'pdf.bz2' => ['application/x-bzpdf'],
        'pdf.gz' => ['application/x-gzpdf'],
        'pdf.lz' => ['application/x-lzpdf'],
        'pdf.xz' => ['application/x-xzpdf'],
        'pef' => ['image/x-pentax-pef'],
        'pem' => ['application/x-x509-ca-cert'],
        'perl' => ['application/x-perl', 'text/x-perl'],
        'pfa' => ['application/x-font-type1'],
        'pfb' => ['application/x-font-type1'],
        'pfm' => ['application/x-font-type1', 'image/x-pfm'],
        'pfr' => ['application/font-tdpfr', 'application/vnd.truedoc'],
        'pfx' => ['application/pkcs12', 'application/x-pkcs12'],
        'pgm' => ['image/x-portable-graymap'],
        'pgn' => ['application/vnd.chess-pgn', 'application/x-chess-pgn'],
        'pgp' => ['application/pgp', 'application/pgp-encrypted', 'application/pgp-keys', 'application/pgp-signature'],
        'php' => ['application/x-php', 'application/x-httpd-php'],
        'php3' => ['application/x-php'],
        'php4' => ['application/x-php'],
        'php5' => ['application/x-php'],
        'phps' => ['application/x-php'],
        'pic' => ['image/x-pict'],
        'pict' => ['image/x-pict'],
        'pict1' => ['image/x-pict'],
        'pict2' => ['image/x-pict'],
        'pk' => ['application/x-tex-pk'],
        'pkg' => ['application/x-xar'],
        'pki' => ['application/pkixcmp'],
        'pkipath' => ['application/pkix-pkipath'],
        'pkpass' => ['application/vnd.apple.pkpass'],
        'pkr' => ['application/pgp-keys'],
        'pl' => ['application/x-perl', 'text/x-perl'],
        'pla' => ['audio/x-iriver-pla'],
        'plb' => ['application/vnd.3gpp.pic-bw-large'],
        'plc' => ['application/vnd.mobius.plc'],
        'plf' => ['application/vnd.pocketlearn'],
        'pln' => ['application/x-planperfect'],
        'pls' => ['application/pls', 'application/pls+xml', 'audio/scpls', 'audio/x-scpls'],
        'pm' => ['application/x-pagemaker', 'application/x-perl', 'text/x-perl'],
        'pm6' => ['application/x-pagemaker'],
        'pmd' => ['application/x-pagemaker'],
        'pml' => ['application/vnd.ctc-posml'],
        'png' => ['image/png', 'image/apng', 'image/vnd.mozilla.apng'],
        'pnm' => ['image/x-portable-anymap'],
        'pntg' => ['image/x-macpaint'],
        'po' => ['application/x-gettext', 'text/x-gettext-translation', 'text/x-po'],
        'pod' => ['application/x-perl', 'text/x-perl'],
        'por' => ['application/x-spss-por'],
        'portpkg' => ['application/vnd.macports.portpkg'],
        'pot' => ['application/mspowerpoint', 'application/powerpoint', 'application/vnd.ms-powerpoint', 'application/x-mspowerpoint', 'text/x-gettext-translation-template', 'text/x-pot'],
        'potm' => ['application/vnd.ms-powerpoint.template.macroenabled.12'],
        'potx' => ['application/vnd.openxmlformats-officedocument.presentationml.template'],
        'ppam' => ['application/vnd.ms-powerpoint.addin.macroenabled.12'],
        'ppd' => ['application/vnd.cups-ppd'],
        'ppm' => ['image/x-portable-pixmap'],
        'pps' => ['application/mspowerpoint', 'application/powerpoint', 'application/vnd.ms-powerpoint', 'application/x-mspowerpoint'],
        'ppsm' => ['application/vnd.ms-powerpoint.slideshow.macroenabled.12'],
        'ppsx' => ['application/vnd.openxmlformats-officedocument.presentationml.slideshow'],
        'ppt' => ['application/vnd.ms-powerpoint', 'application/mspowerpoint', 'application/powerpoint', 'application/x-mspowerpoint'],
        'pptm' => ['application/vnd.ms-powerpoint.presentation.macroenabled.12'],
        'pptx' => ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
        'ppz' => ['application/mspowerpoint', 'application/powerpoint', 'application/vnd.ms-powerpoint', 'application/x-mspowerpoint'],
        'pqa' => ['application/vnd.palm', 'application/x-palm-database'],
        'prc' => ['application/vnd.palm', 'application/x-mobipocket-ebook', 'application/x-palm-database', 'application/x-pilot', 'model/prc'],
        'pre' => ['application/vnd.lotus-freelance'],
        'prf' => ['application/pics-rules'],
        'provx' => ['application/provenance+xml'],
        'ps' => ['application/postscript'],
        'ps.bz2' => ['application/x-bzpostscript'],
        'ps.gz' => ['application/x-gzpostscript'],
        'ps1' => ['application/x-powershell'],
        'psb' => ['application/vnd.3gpp.pic-bw-small'],
        'psd' => ['application/photoshop', 'application/x-photoshop', 'image/photoshop', 'image/psd', 'image/vnd.adobe.photoshop', 'image/x-photoshop', 'image/x-psd'],
        'psf' => ['application/x-font-linux-psf', 'audio/x-psf'],
        'psf.gz' => ['application/x-gz-font-linux-psf'],
        'psflib' => ['audio/x-psflib'],
        'psid' => ['audio/prs.sid'],
        'pskcxml' => ['application/pskc+xml'],
        'psw' => ['application/x-pocket-word'],
        'pti' => ['image/prs.pti'],
        'ptid' => ['application/vnd.pvi.ptid1'],
        'pub' => ['application/vnd.ms-publisher', 'application/x-mspublisher'],
        'pvb' => ['application/vnd.3gpp.pic-bw-var'],
        'pw' => ['application/x-pw'],
        'pwn' => ['application/vnd.3m.post-it-notes'],
        'pxd' => ['text/x-cython'],
        'pxi' => ['text/x-cython'],
        'pxr' => ['image/x-pxr'],
        'py' => ['text/x-python', 'text/x-python2', 'text/x-python3'],
        'py2' => ['text/x-python2'],
        'py3' => ['text/x-python3'],
        'pya' => ['audio/vnd.ms-playready.media.pya'],
        'pyc' => ['application/x-python-bytecode'],
        'pyi' => ['text/x-python3'],
        'pyo' => ['application/x-python-bytecode', 'model/vnd.pytha.pyox'],
        'pyox' => ['model/vnd.pytha.pyox'],
        'pys' => ['application/x-pyspread-bz-spreadsheet'],
        'pysu' => ['application/x-pyspread-spreadsheet'],
        'pyv' => ['video/vnd.ms-playready.media.pyv'],
        'pyx' => ['text/x-cython'],
        'qam' => ['application/vnd.epson.quickanime'],
        'qbo' => ['application/vnd.intu.qbo'],
        'qbrew' => ['application/x-qbrew'],
        'qcow' => ['application/x-qemu-disk'],
        'qcow2' => ['application/x-qemu-disk'],
        'qd' => ['application/x-fd-file', 'application/x-raw-floppy-disk-image'],
        'qed' => ['application/x-qed-disk'],
        'qfx' => ['application/vnd.intu.qfx'],
        'qif' => ['application/x-qw', 'image/x-quicktime'],
        'qml' => ['text/x-qml'],
        'qmlproject' => ['text/x-qml'],
        'qmltypes' => ['text/x-qml'],
        'qoi' => ['image/qoi'],
        'qp' => ['application/x-qpress'],
        'qps' => ['application/vnd.publishare-delta-tree'],
        'qpw' => ['application/x-quattropro'],
        'qs' => ['application/sparql-query'],
        'qt' => ['video/quicktime'],
        'qti' => ['application/x-qtiplot'],
        'qti.gz' => ['application/x-qtiplot'],
        'qtif' => ['image/x-quicktime'],
        'qtl' => ['application/x-quicktime-media-link', 'application/x-quicktimeplayer'],
        'qtvr' => ['video/quicktime'],
        'qwd' => ['application/vnd.quark.quarkxpress'],
        'qwt' => ['application/vnd.quark.quarkxpress'],
        'qxb' => ['application/vnd.quark.quarkxpress'],
        'qxd' => ['application/vnd.quark.quarkxpress'],
        'qxl' => ['application/vnd.quark.quarkxpress'],
        'qxp' => ['application/vnd.quark.quarkxpress'],
        'qxt' => ['application/vnd.quark.quarkxpress'],
        'ra' => ['audio/vnd.m-realaudio', 'audio/vnd.rn-realaudio', 'audio/x-pn-realaudio', 'audio/x-realaudio'],
        'raf' => ['image/x-fuji-raf'],
        'ram' => ['application/ram', 'audio/x-pn-realaudio'],
        'raml' => ['application/raml+yaml'],
        'rapd' => ['application/route-apd+xml'],
        'rar' => ['application/x-rar-compressed', 'application/vnd.rar', 'application/x-rar'],
        'ras' => ['image/x-cmu-raster'],
        'raw' => ['image/x-panasonic-raw', 'image/x-panasonic-rw'],
        'raw-disk-image' => ['application/vnd.efi.img', 'application/x-raw-disk-image'],
        'raw-disk-image.xz' => ['application/x-raw-disk-image-xz-compressed'],
        'rax' => ['audio/vnd.m-realaudio', 'audio/vnd.rn-realaudio', 'audio/x-pn-realaudio'],
        'rb' => ['application/x-ruby'],
        'rcprofile' => ['application/vnd.ipunplugged.rcprofile'],
        'rdf' => ['application/rdf+xml', 'text/rdf'],
        'rdfs' => ['application/rdf+xml', 'text/rdf'],
        'rdz' => ['application/vnd.data-vision.rdz'],
        'reg' => ['text/x-ms-regedit'],
        'rej' => ['application/x-reject', 'text/x-reject'],
        'relo' => ['application/p2p-overlay+xml'],
        'rep' => ['application/vnd.businessobjects'],
        'res' => ['application/x-dtbresource+xml', 'application/x-godot-resource'],
        'rgb' => ['image/x-rgb'],
        'rif' => ['application/reginfo+xml'],
        'rip' => ['audio/vnd.rip'],
        'ris' => ['application/x-research-info-systems'],
        'rl' => ['application/resource-lists+xml'],
        'rlc' => ['image/vnd.fujixerox.edmics-rlc'],
        'rld' => ['application/resource-lists-diff+xml'],
        'rle' => ['image/rle'],
        'rm' => ['application/vnd.rn-realmedia', 'application/vnd.rn-realmedia-vbr'],
        'rmi' => ['audio/midi'],
        'rmj' => ['application/vnd.rn-realmedia', 'application/vnd.rn-realmedia-vbr'],
        'rmm' => ['application/vnd.rn-realmedia', 'application/vnd.rn-realmedia-vbr'],
        'rmp' => ['audio/x-pn-realaudio-plugin'],
        'rms' => ['application/vnd.jcp.javame.midlet-rms', 'application/vnd.rn-realmedia', 'application/vnd.rn-realmedia-vbr'],
        'rmvb' => ['application/vnd.rn-realmedia', 'application/vnd.rn-realmedia-vbr'],
        'rmx' => ['application/vnd.rn-realmedia', 'application/vnd.rn-realmedia-vbr'],
        'rnc' => ['application/relax-ng-compact-syntax', 'application/x-rnc'],
        'rng' => ['application/xml', 'text/xml'],
        'roa' => ['application/rpki-roa'],
        'roff' => ['application/x-troff', 'text/troff', 'text/x-troff'],
        'ros' => ['text/x-common-lisp'],
        'rp' => ['image/vnd.rn-realpix'],
        'rp9' => ['application/vnd.cloanto.rp9'],
        'rpm' => ['application/x-redhat-package-manager', 'application/x-rpm'],
        'rpss' => ['application/vnd.nokia.radio-presets'],
        'rpst' => ['application/vnd.nokia.radio-preset'],
        'rq' => ['application/sparql-query'],
        'rs' => ['application/rls-services+xml', 'text/rust'],
        'rsat' => ['application/atsc-rsat+xml'],
        'rsd' => ['application/rsd+xml'],
        'rsheet' => ['application/urc-ressheet+xml'],
        'rss' => ['application/rss+xml', 'text/rss'],
        'rst' => ['text/x-rst'],
        'rt' => ['text/vnd.rn-realtext'],
        'rtf' => ['application/rtf', 'text/rtf'],
        'rtx' => ['text/richtext'],
        'run' => ['application/x-makeself'],
        'rusd' => ['application/route-usd+xml'],
        'rv' => ['video/vnd.rn-realvideo', 'video/x-real-video'],
        'rvx' => ['video/vnd.rn-realvideo', 'video/x-real-video'],
        'rw2' => ['image/x-panasonic-raw2', 'image/x-panasonic-rw2'],
        'rz' => ['application/x-rzip'],
        's' => ['text/x-asm'],
        's3m' => ['audio/s3m', 'audio/x-s3m'],
        'saf' => ['application/vnd.yamaha.smaf-audio'],
        'sage' => ['text/x-sagemath'],
        'sam' => ['application/x-amipro'],
        'sami' => ['application/x-sami'],
        'sap' => ['application/x-sap-file', 'application/x-thomson-sap-image'],
        'sass' => ['text/x-sass'],
        'sav' => ['application/x-spss-sav', 'application/x-spss-savefile'],
        'sbml' => ['application/sbml+xml'],
        'sc' => ['application/vnd.ibm.secure-container', 'text/x-scala'],
        'scala' => ['text/x-scala'],
        'scd' => ['application/x-msschedule'],
        'scm' => ['application/vnd.lotus-screencam', 'text/x-scheme'],
        'scn' => ['application/x-godot-scene'],
        'scope' => ['text/x-systemd-unit'],
        'scq' => ['application/scvp-cv-request'],
        'scr' => ['application/vnd.microsoft.portable-executable', 'application/x-ms-dos-executable', 'application/x-ms-ne-executable', 'application/x-msdownload'],
        'scs' => ['application/scvp-cv-response'],
        'scss' => ['text/x-scss'],
        'sct' => ['image/x-sct'],
        'scurl' => ['text/vnd.curl.scurl'],
        'sda' => ['application/vnd.stardivision.draw', 'application/x-stardraw'],
        'sdc' => ['application/vnd.stardivision.calc', 'application/x-starcalc'],
        'sdd' => ['application/vnd.stardivision.impress', 'application/x-starimpress'],
        'sdkd' => ['application/vnd.solent.sdkm+xml'],
        'sdkm' => ['application/vnd.solent.sdkm+xml'],
        'sdm' => ['application/vnd.stardivision.mail'],
        'sdp' => ['application/sdp', 'application/vnd.sdp', 'application/vnd.stardivision.impress-packed', 'application/x-sdp'],
        'sds' => ['application/vnd.stardivision.chart', 'application/x-starchart'],
        'sdw' => ['application/vnd.stardivision.writer', 'application/x-starwriter'],
        'sea' => ['application/x-sea'],
        'see' => ['application/vnd.seemail'],
        'seed' => ['application/vnd.fdsn.seed'],
        'sema' => ['application/vnd.sema'],
        'semd' => ['application/vnd.semd'],
        'semf' => ['application/vnd.semf'],
        'senmlx' => ['application/senml+xml'],
        'sensmlx' => ['application/sensml+xml'],
        'ser' => ['application/java-serialized-object'],
        'service' => ['text/x-dbus-service', 'text/x-systemd-unit'],
        'setpay' => ['application/set-payment-initiation'],
        'setreg' => ['application/set-registration-initiation'],
        'sfc' => ['application/vnd.nintendo.snes.rom', 'application/x-snes-rom'],
        'sfd-hdstx' => ['application/vnd.hydrostatix.sof-data'],
        'sfs' => ['application/vnd.spotfire.sfs', 'application/vnd.squashfs'],
        'sfv' => ['text/x-sfv'],
        'sg' => ['application/x-sg1000-rom'],
        'sgb' => ['application/x-gameboy-rom'],
        'sgd' => ['application/x-genesis-rom'],
        'sgf' => ['application/x-go-sgf'],
        'sgi' => ['image/sgi', 'image/x-sgi'],
        'sgl' => ['application/vnd.stardivision.writer-global', 'application/x-starwriter-global'],
        'sgm' => ['text/sgml'],
        'sgml' => ['text/sgml'],
        'sh' => ['application/x-sh', 'application/x-shellscript', 'text/x-sh'],
        'shape' => ['application/x-dia-shape'],
        'shar' => ['application/x-shar'],
        'shex' => ['text/shex'],
        'shf' => ['application/shf+xml'],
        'shn' => ['application/x-shorten', 'audio/x-shorten'],
        'shtml' => ['text/html'],
        'siag' => ['application/x-siag'],
        'sid' => ['audio/prs.sid', 'image/x-mrsid-image'],
        'sieve' => ['application/sieve'],
        'sig' => ['application/pgp-signature'],
        'sik' => ['application/x-trash'],
        'sil' => ['audio/silk'],
        'silo' => ['model/mesh'],
        'sis' => ['application/vnd.symbian.install'],
        'sisx' => ['application/vnd.symbian.install', 'x-epoc/x-sisx-app'],
        'sit' => ['application/x-stuffit', 'application/stuffit', 'application/x-sit'],
        'sitx' => ['application/x-sitx', 'application/x-stuffitx'],
        'siv' => ['application/sieve'],
        'sk' => ['image/x-skencil'],
        'sk1' => ['image/x-skencil'],
        'skd' => ['application/vnd.koan'],
        'skm' => ['application/vnd.koan'],
        'skp' => ['application/vnd.koan'],
        'skr' => ['application/pgp-keys'],
        'skt' => ['application/vnd.koan'],
        'sldm' => ['application/vnd.ms-powerpoint.slide.macroenabled.12'],
        'sldx' => ['application/vnd.openxmlformats-officedocument.presentationml.slide'],
        'slice' => ['text/x-systemd-unit'],
        'slim' => ['text/slim'],
        'slk' => ['application/x-sylk', 'text/spreadsheet'],
        'slm' => ['text/slim'],
        'sls' => ['application/route-s-tsid+xml'],
        'slt' => ['application/vnd.epson.salt'],
        'sm' => ['application/vnd.stepmania.stepchart'],
        'smaf' => ['application/vnd.smaf', 'application/x-smaf'],
        'smc' => ['application/vnd.nintendo.snes.rom', 'application/x-snes-rom'],
        'smd' => ['application/x-genesis-rom', 'application/x-starmail'],
        'smf' => ['application/vnd.stardivision.math', 'application/x-starmath'],
        'smi' => ['application/smil', 'application/smil+xml', 'application/x-sami'],
        'smil' => ['application/smil', 'application/smil+xml'],
        'smk' => ['video/vnd.radgamettools.smacker'],
        'sml' => ['application/smil', 'application/smil+xml'],
        'sms' => ['application/x-sms-rom'],
        'smv' => ['video/x-smv'],
        'smzip' => ['application/vnd.stepmania.package'],
        'snap' => ['application/vnd.snap'],
        'snd' => ['audio/basic'],
        'snf' => ['application/x-font-snf'],
        'so' => ['application/x-sharedlib'],
        'socket' => ['text/x-systemd-unit'],
        'spc' => ['application/x-pkcs7-certificates'],
        'spd' => ['application/x-font-speedo'],
        'spdx' => ['text/spdx'],
        'spec' => ['text/x-rpm-spec'],
        'spf' => ['application/vnd.yamaha.smaf-phrase'],
        'spl' => ['application/futuresplash', 'application/vnd.adobe.flash.movie', 'application/x-futuresplash', 'application/x-shockwave-flash'],
        'spm' => ['application/x-source-rpm'],
        'spot' => ['text/vnd.in3d.spot'],
        'spp' => ['application/scvp-vp-response'],
        'spq' => ['application/scvp-vp-request'],
        'spx' => ['application/x-apple-systemprofiler+xml', 'audio/ogg', 'audio/x-speex', 'audio/x-speex+ogg'],
        'sqfs' => ['application/vnd.squashfs'],
        'sql' => ['application/sql', 'application/x-sql', 'text/x-sql'],
        'sqlite2' => ['application/x-sqlite2'],
        'sqlite3' => ['application/vnd.sqlite3', 'application/x-sqlite3'],
        'sqsh' => ['application/vnd.squashfs'],
        'squashfs' => ['application/vnd.squashfs'],
        'sr2' => ['image/x-sony-sr2'],
        'src' => ['application/x-wais-source'],
        'src.rpm' => ['application/x-source-rpm'],
        'srf' => ['image/x-sony-srf'],
        'srt' => ['application/x-srt', 'application/x-subrip'],
        'sru' => ['application/sru+xml'],
        'srx' => ['application/sparql-results+xml'],
        'ss' => ['text/x-scheme'],
        'ssa' => ['text/x-ssa'],
        'ssdl' => ['application/ssdl+xml'],
        'sse' => ['application/vnd.kodak-descriptor'],
        'ssf' => ['application/vnd.epson.ssf'],
        'ssml' => ['application/ssml+xml'],
        'st' => ['application/vnd.sailingtracker.track'],
        'stc' => ['application/vnd.sun.xml.calc.template'],
        'std' => ['application/vnd.sun.xml.draw.template'],
        'step' => ['model/step'],
        'stf' => ['application/vnd.wt.stf'],
        'sti' => ['application/vnd.sun.xml.impress.template'],
        'stk' => ['application/hyperstudio'],
        'stl' => ['application/vnd.ms-pki.stl', 'model/stl', 'model/x.stl-ascii', 'model/x.stl-binary'],
        'stm' => ['audio/x-stm'],
        'stp' => ['model/step'],
        'stpx' => ['model/step+xml'],
        'stpxz' => ['model/step-xml+zip'],
        'stpz' => ['model/step+zip'],
        'str' => ['application/vnd.pg.format'],
        'stw' => ['application/vnd.sun.xml.writer.template'],
        'sty' => ['application/x-tex', 'text/x-tex'],
        'styl' => ['text/stylus'],
        'stylus' => ['text/stylus'],
        'sub' => ['image/vnd.dvb.subtitle', 'text/vnd.dvb.subtitle', 'text/x-microdvd', 'text/x-mpsub', 'text/x-subviewer'],
        'sun' => ['image/x-sun-raster'],
        'sus' => ['application/vnd.sus-calendar'],
        'susp' => ['application/vnd.sus-calendar'],
        'sv' => ['text/x-svsrc'],
        'sv4cpio' => ['application/x-sv4cpio'],
        'sv4crc' => ['application/x-sv4crc'],
        'svc' => ['application/vnd.dvb.service'],
        'svd' => ['application/vnd.svd'],
        'svg' => ['image/svg+xml', 'image/svg'],
        'svg.gz' => ['image/svg+xml-compressed'],
        'svgz' => ['image/svg+xml', 'image/svg+xml-compressed'],
        'svh' => ['text/x-svhdr'],
        'swa' => ['application/x-director'],
        'swap' => ['text/x-systemd-unit'],
        'swf' => ['application/futuresplash', 'application/vnd.adobe.flash.movie', 'application/x-shockwave-flash'],
        'swi' => ['application/vnd.aristanetworks.swi'],
        'swidtag' => ['application/swid+xml'],
        'swm' => ['application/x-ms-wim'],
        'sxc' => ['application/vnd.sun.xml.calc'],
        'sxd' => ['application/vnd.sun.xml.draw'],
        'sxg' => ['application/vnd.sun.xml.writer.global'],
        'sxi' => ['application/vnd.sun.xml.impress'],
        'sxm' => ['application/vnd.sun.xml.math'],
        'sxw' => ['application/vnd.sun.xml.writer'],
        'sylk' => ['application/x-sylk', 'text/spreadsheet'],
        'sys' => ['application/vnd.microsoft.portable-executable'],
        't' => ['application/x-perl', 'application/x-troff', 'text/troff', 'text/x-perl', 'text/x-troff'],
        't2t' => ['text/x-txt2tags'],
        't3' => ['application/x-t3vm-image'],
        't38' => ['image/t38'],
        'taglet' => ['application/vnd.mynfc'],
        'tak' => ['audio/x-tak'],
        'tao' => ['application/vnd.tao.intent-module-archive'],
        'tap' => ['image/vnd.tencent.tap'],
        'tar' => ['application/x-tar', 'application/x-gtar'],
        'tar.Z' => ['application/x-tarz'],
        'tar.bz' => ['application/x-bzip1-compressed-tar'],
        'tar.bz2' => ['application/x-bzip-compressed-tar', 'application/x-bzip2-compressed-tar'],
        'tar.bz3' => ['application/x-bzip3-compressed-tar'],
        'tar.gz' => ['application/x-compressed-tar'],
        'tar.lrz' => ['application/x-lrzip-compressed-tar'],
        'tar.lz' => ['application/x-lzip-compressed-tar'],
        'tar.lz4' => ['application/x-lz4-compressed-tar'],
        'tar.lzma' => ['application/x-lzma-compressed-tar'],
        'tar.lzo' => ['application/x-tzo'],
        'tar.rz' => ['application/x-rzip-compressed-tar'],
        'tar.xz' => ['application/x-xz-compressed-tar'],
        'tar.zst' => ['application/x-zstd-compressed-tar'],
        'target' => ['text/x-systemd-unit'],
        'taz' => ['application/x-tarz'],
        'tb2' => ['application/x-bzip-compressed-tar', 'application/x-bzip2-compressed-tar'],
        'tbz' => ['application/x-bzip1-compressed-tar'],
        'tbz2' => ['application/x-bzip-compressed-tar', 'application/x-bzip2-compressed-tar'],
        'tbz3' => ['application/x-bzip3-compressed-tar'],
        'tcap' => ['application/vnd.3gpp2.tcap'],
        'tcl' => ['application/x-tcl', 'text/tcl', 'text/x-tcl'],
        'td' => ['application/urc-targetdesc+xml'],
        'teacher' => ['application/vnd.smart.teacher'],
        'tei' => ['application/tei+xml'],
        'teicorpus' => ['application/tei+xml'],
        'tex' => ['application/x-tex', 'text/x-tex'],
        'texi' => ['application/x-texinfo', 'text/x-texinfo'],
        'texinfo' => ['application/x-texinfo', 'text/x-texinfo'],
        'text' => ['text/plain'],
        'tfi' => ['application/thraud+xml'],
        'tfm' => ['application/x-tex-tfm'],
        'tfx' => ['image/tiff-fx'],
        'tga' => ['application/tga', 'application/x-targa', 'application/x-tga', 'image/targa', 'image/tga', 'image/x-icb', 'image/x-targa', 'image/x-tga'],
        'tgz' => ['application/x-compressed-tar'],
        'theme' => ['application/x-theme'],
        'themepack' => ['application/x-windows-themepack'],
        'thmx' => ['application/vnd.ms-officetheme'],
        'tif' => ['image/tiff'],
        'tiff' => ['image/tiff'],
        'timer' => ['text/x-systemd-unit'],
        'tk' => ['application/x-tcl', 'text/tcl', 'text/x-tcl'],
        'tlrz' => ['application/x-lrzip-compressed-tar'],
        'tlz' => ['application/x-lzma-compressed-tar'],
        'tmo' => ['application/vnd.tmobile-livetv'],
        'tmx' => ['application/x-tiled-tmx'],
        'tnef' => ['application/ms-tnef', 'application/vnd.ms-tnef'],
        'tnf' => ['application/ms-tnef', 'application/vnd.ms-tnef'],
        'toc' => ['application/x-cdrdao-toc'],
        'toml' => ['application/toml'],
        'torrent' => ['application/x-bittorrent'],
        'tpic' => ['application/tga', 'application/x-targa', 'application/x-tga', 'image/targa', 'image/tga', 'image/x-icb', 'image/x-targa', 'image/x-tga'],
        'tpl' => ['application/vnd.groove-tool-template'],
        'tpt' => ['application/vnd.trid.tpt'],
        'tr' => ['application/x-troff', 'text/troff', 'text/x-troff'],
        'tra' => ['application/vnd.trueapp'],
        'tres' => ['application/x-godot-resource'],
        'trig' => ['application/trig', 'application/x-trig'],
        'trm' => ['application/x-msterminal'],
        'trz' => ['application/x-rzip-compressed-tar'],
        'ts' => ['application/x-linguist', 'text/vnd.qt.linguist', 'text/vnd.trolltech.linguist', 'video/mp2t'],
        'tscn' => ['application/x-godot-scene'],
        'tsd' => ['application/timestamped-data'],
        'tsv' => ['text/tab-separated-values'],
        'tsx' => ['application/x-tiled-tsx'],
        'tta' => ['audio/tta', 'audio/x-tta'],
        'ttc' => ['font/collection'],
        'ttf' => ['application/x-font-truetype', 'application/x-font-ttf', 'font/ttf'],
        'ttl' => ['text/turtle'],
        'ttml' => ['application/ttml+xml'],
        'ttx' => ['application/x-font-ttx'],
        'twd' => ['application/vnd.simtech-mindmapper'],
        'twds' => ['application/vnd.simtech-mindmapper'],
        'twig' => ['text/x-twig'],
        'txd' => ['application/vnd.genomatix.tuxedo'],
        'txf' => ['application/vnd.mobius.txf'],
        'txt' => ['text/plain'],
        'txz' => ['application/x-xz-compressed-tar'],
        'typ' => ['text/x-typst'],
        'tzo' => ['application/x-tzo'],
        'tzst' => ['application/x-zstd-compressed-tar'],
        'u32' => ['application/x-authorware-bin'],
        'u3d' => ['model/u3d'],
        'u8dsn' => ['message/global-delivery-status'],
        'u8hdr' => ['message/global-headers'],
        'u8mdn' => ['message/global-disposition-notification'],
        'u8msg' => ['message/global'],
        'ubj' => ['application/ubjson'],
        'udeb' => ['application/vnd.debian.binary-package', 'application/x-deb', 'application/x-debian-package'],
        'ufd' => ['application/vnd.ufdl'],
        'ufdl' => ['application/vnd.ufdl'],
        'ufraw' => ['application/x-ufraw'],
        'ui' => ['application/x-designer', 'application/x-gtk-builder'],
        'uil' => ['text/x-uil'],
        'ult' => ['audio/x-mod'],
        'ulx' => ['application/x-glulx'],
        'umj' => ['application/vnd.umajin'],
        'unf' => ['application/x-nes-rom'],
        'uni' => ['audio/x-mod'],
        'unif' => ['application/x-nes-rom'],
        'unityweb' => ['application/vnd.unity'],
        'uo' => ['application/vnd.uoml+xml'],
        'uoml' => ['application/vnd.uoml+xml'],
        'uri' => ['text/uri-list'],
        'uris' => ['text/uri-list'],
        'url' => ['application/x-mswinurl'],
        'urls' => ['text/uri-list'],
        'usda' => ['model/vnd.usda'],
        'usdz' => ['model/vnd.usdz+zip'],
        'ustar' => ['application/x-ustar'],
        'utz' => ['application/vnd.uiq.theme'],
        'uu' => ['text/x-uuencode'],
        'uue' => ['text/x-uuencode', 'zz-application/zz-winassoc-uu'],
        'uva' => ['audio/vnd.dece.audio'],
        'uvd' => ['application/vnd.dece.data'],
        'uvf' => ['application/vnd.dece.data'],
        'uvg' => ['image/vnd.dece.graphic'],
        'uvh' => ['video/vnd.dece.hd'],
        'uvi' => ['image/vnd.dece.graphic'],
        'uvm' => ['video/vnd.dece.mobile'],
        'uvp' => ['video/vnd.dece.pd'],
        'uvs' => ['video/vnd.dece.sd'],
        'uvt' => ['application/vnd.dece.ttml+xml'],
        'uvu' => ['video/vnd.uvvu.mp4'],
        'uvv' => ['video/vnd.dece.video'],
        'uvva' => ['audio/vnd.dece.audio'],
        'uvvd' => ['application/vnd.dece.data'],
        'uvvf' => ['application/vnd.dece.data'],
        'uvvg' => ['image/vnd.dece.graphic'],
        'uvvh' => ['video/vnd.dece.hd'],
        'uvvi' => ['image/vnd.dece.graphic'],
        'uvvm' => ['video/vnd.dece.mobile'],
        'uvvp' => ['video/vnd.dece.pd'],
        'uvvs' => ['video/vnd.dece.sd'],
        'uvvt' => ['application/vnd.dece.ttml+xml'],
        'uvvu' => ['video/vnd.uvvu.mp4'],
        'uvvv' => ['video/vnd.dece.video'],
        'uvvx' => ['application/vnd.dece.unspecified'],
        'uvvz' => ['application/vnd.dece.zip'],
        'uvx' => ['application/vnd.dece.unspecified'],
        'uvz' => ['application/vnd.dece.zip'],
        'v' => ['text/x-verilog'],
        'v64' => ['application/x-n64-rom'],
        'vala' => ['text/x-vala'],
        'vapi' => ['text/x-vala'],
        'vb' => ['application/x-virtual-boy-rom', 'text/x-vb'],
        'vbe' => ['text/vbscript.encode'],
        'vbox' => ['application/x-virtualbox-vbox'],
        'vbox-extpack' => ['application/x-virtualbox-vbox-extpack'],
        'vbs' => ['text/vbs', 'text/vbscript'],
        'vcard' => ['text/directory', 'text/vcard', 'text/x-vcard'],
        'vcd' => ['application/x-cdlink'],
        'vcf' => ['text/x-vcard', 'text/directory', 'text/vcard'],
        'vcg' => ['application/vnd.groove-vcard'],
        'vcs' => ['application/ics', 'text/calendar', 'text/x-vcalendar'],
        'vct' => ['text/directory', 'text/vcard', 'text/x-vcard'],
        'vcx' => ['application/vnd.vcx'],
        'vda' => ['application/tga', 'application/x-targa', 'application/x-tga', 'image/targa', 'image/tga', 'image/x-icb', 'image/x-targa', 'image/x-tga'],
        'vdi' => ['application/x-vdi-disk', 'application/x-virtualbox-vdi'],
        'vds' => ['model/vnd.sap.vds'],
        'vhd' => ['application/x-vhd-disk', 'application/x-virtualbox-vhd', 'text/x-vhdl'],
        'vhdl' => ['text/x-vhdl'],
        'vhdx' => ['application/x-vhdx-disk', 'application/x-virtualbox-vhdx'],
        'vis' => ['application/vnd.visionary'],
        'viv' => ['video/vivo', 'video/vnd.vivo'],
        'vivo' => ['video/vivo', 'video/vnd.vivo'],
        'vlc' => ['application/m3u', 'audio/m3u', 'audio/mpegurl', 'audio/x-m3u', 'audio/x-mp3-playlist', 'audio/x-mpegurl'],
        'vmdk' => ['application/x-virtualbox-vmdk', 'application/x-vmdk-disk'],
        'vob' => ['video/mpeg', 'video/mpeg-system', 'video/x-mpeg', 'video/x-mpeg-system', 'video/x-mpeg2', 'video/x-ms-vob'],
        'voc' => ['audio/x-voc'],
        'vor' => ['application/vnd.stardivision.writer', 'application/x-starwriter'],
        'vox' => ['application/x-authorware-bin'],
        'vpc' => ['application/x-vhd-disk', 'application/x-virtualbox-vhd'],
        'vrm' => ['model/vrml'],
        'vrml' => ['model/vrml'],
        'vsd' => ['application/vnd.visio'],
        'vsdm' => ['application/vnd.ms-visio.drawing.macroenabled.main+xml'],
        'vsdx' => ['application/vnd.ms-visio.drawing.main+xml'],
        'vsf' => ['application/vnd.vsf'],
        'vss' => ['application/vnd.visio'],
        'vssm' => ['application/vnd.ms-visio.stencil.macroenabled.main+xml'],
        'vssx' => ['application/vnd.ms-visio.stencil.main+xml'],
        'vst' => ['application/tga', 'application/vnd.visio', 'application/x-targa', 'application/x-tga', 'image/targa', 'image/tga', 'image/x-icb', 'image/x-targa', 'image/x-tga'],
        'vstm' => ['application/vnd.ms-visio.template.macroenabled.main+xml'],
        'vstx' => ['application/vnd.ms-visio.template.main+xml'],
        'vsw' => ['application/vnd.visio'],
        'vtf' => ['image/vnd.valve.source.texture'],
        'vtt' => ['text/vtt'],
        'vtu' => ['model/vnd.vtu'],
        'vxml' => ['application/voicexml+xml'],
        'w3d' => ['application/x-director'],
        'wad' => ['application/x-doom', 'application/x-doom-wad', 'application/x-wii-wad'],
        'wadl' => ['application/vnd.sun.wadl+xml'],
        'war' => ['application/java-archive'],
        'wasm' => ['application/wasm'],
        'wav' => ['audio/wav', 'audio/vnd.wave', 'audio/wave', 'audio/x-wav'],
        'wax' => ['application/x-ms-asx', 'audio/x-ms-asx', 'audio/x-ms-wax', 'video/x-ms-wax', 'video/x-ms-wmx', 'video/x-ms-wvx'],
        'wb1' => ['application/x-quattropro'],
        'wb2' => ['application/x-quattropro'],
        'wb3' => ['application/x-quattropro'],
        'wbmp' => ['image/vnd.wap.wbmp'],
        'wbs' => ['application/vnd.criticaltools.wbs+xml'],
        'wbxml' => ['application/vnd.wap.wbxml'],
        'wcm' => ['application/vnd.ms-works'],
        'wdb' => ['application/vnd.ms-works'],
        'wdp' => ['image/jxr', 'image/vnd.ms-photo'],
        'weba' => ['audio/webm'],
        'webapp' => ['application/x-web-app-manifest+json'],
        'webm' => ['video/webm'],
        'webmanifest' => ['application/manifest+json'],
        'webp' => ['image/webp'],
        'wg' => ['application/vnd.pmi.widget'],
        'wgsl' => ['text/wgsl'],
        'wgt' => ['application/widget'],
        'wif' => ['application/watcherinfo+xml'],
        'wim' => ['application/x-ms-wim'],
        'wk1' => ['application/lotus123', 'application/vnd.lotus-1-2-3', 'application/wk1', 'application/x-123', 'application/x-lotus123', 'zz-application/zz-winassoc-123'],
        'wk3' => ['application/lotus123', 'application/vnd.lotus-1-2-3', 'application/wk1', 'application/x-123', 'application/x-lotus123', 'zz-application/zz-winassoc-123'],
        'wk4' => ['application/lotus123', 'application/vnd.lotus-1-2-3', 'application/wk1', 'application/x-123', 'application/x-lotus123', 'zz-application/zz-winassoc-123'],
        'wkdownload' => ['application/x-partial-download'],
        'wks' => ['application/lotus123', 'application/vnd.lotus-1-2-3', 'application/vnd.ms-works', 'application/wk1', 'application/x-123', 'application/x-lotus123', 'zz-application/zz-winassoc-123'],
        'wm' => ['video/x-ms-wm'],
        'wma' => ['audio/x-ms-wma', 'audio/wma'],
        'wmd' => ['application/x-ms-wmd'],
        'wmf' => ['application/wmf', 'application/x-msmetafile', 'application/x-wmf', 'image/wmf', 'image/x-win-metafile', 'image/x-wmf'],
        'wml' => ['text/vnd.wap.wml'],
        'wmlc' => ['application/vnd.wap.wmlc'],
        'wmls' => ['text/vnd.wap.wmlscript'],
        'wmlsc' => ['application/vnd.wap.wmlscriptc'],
        'wmv' => ['audio/x-ms-wmv', 'video/x-ms-wmv'],
        'wmx' => ['application/x-ms-asx', 'audio/x-ms-asx', 'video/x-ms-wax', 'video/x-ms-wmx', 'video/x-ms-wvx'],
        'wmz' => ['application/x-ms-wmz', 'application/x-msmetafile'],
        'woff' => ['application/font-woff', 'application/x-font-woff', 'font/woff'],
        'woff2' => ['font/woff2'],
        'wp' => ['application/vnd.wordperfect', 'application/wordperfect', 'application/x-wordperfect'],
        'wp4' => ['application/vnd.wordperfect', 'application/wordperfect', 'application/x-wordperfect'],
        'wp5' => ['application/vnd.wordperfect', 'application/wordperfect', 'application/x-wordperfect'],
        'wp6' => ['application/vnd.wordperfect', 'application/wordperfect', 'application/x-wordperfect'],
        'wpd' => ['application/vnd.wordperfect', 'application/wordperfect', 'application/x-wordperfect'],
        'wpg' => ['application/x-wpg'],
        'wpl' => ['application/vnd.ms-wpl'],
        'wpp' => ['application/vnd.wordperfect', 'application/wordperfect', 'application/x-wordperfect'],
        'wps' => ['application/vnd.ms-works'],
        'wqd' => ['application/vnd.wqd'],
        'wri' => ['application/x-mswrite'],
        'wrl' => ['model/vrml'],
        'ws' => ['application/x-wonderswan-rom'],
        'wsc' => ['application/x-wonderswan-color-rom', 'message/vnd.wfa.wsc'],
        'wsdl' => ['application/wsdl+xml'],
        'wsgi' => ['text/x-python'],
        'wspolicy' => ['application/wspolicy+xml'],
        'wtb' => ['application/vnd.webturbo'],
        'wv' => ['audio/x-wavpack'],
        'wvc' => ['audio/x-wavpack-correction'],
        'wvp' => ['audio/x-wavpack'],
        'wvx' => ['application/x-ms-asx', 'audio/x-ms-asx', 'video/x-ms-wax', 'video/x-ms-wmx', 'video/x-ms-wvx'],
        'wwf' => ['application/wwf', 'application/x-wwf'],
        'x32' => ['application/x-authorware-bin'],
        'x3d' => ['model/x3d+xml'],
        'x3db' => ['model/x3d+binary', 'model/x3d+fastinfoset'],
        'x3dbz' => ['model/x3d+binary'],
        'x3dv' => ['model/x3d+vrml', 'model/x3d-vrml'],
        'x3dvz' => ['model/x3d+vrml'],
        'x3dz' => ['model/x3d+xml'],
        'x3f' => ['image/x-sigma-x3f'],
        'x_b' => ['model/vnd.parasolid.transmit.binary'],
        'x_t' => ['model/vnd.parasolid.transmit.text'],
        'xac' => ['application/x-gnucash'],
        'xaml' => ['application/xaml+xml'],
        'xap' => ['application/x-silverlight-app'],
        'xar' => ['application/vnd.xara', 'application/x-xar'],
        'xav' => ['application/xcap-att+xml'],
        'xbap' => ['application/x-ms-xbap'],
        'xbd' => ['application/vnd.fujixerox.docuworks.binder'],
        'xbel' => ['application/x-xbel'],
        'xbl' => ['application/xml', 'text/xml'],
        'xbm' => ['image/x-xbitmap'],
        'xca' => ['application/xcap-caps+xml'],
        'xcf' => ['image/x-xcf'],
        'xcf.bz2' => ['image/x-compressed-xcf'],
        'xcf.gz' => ['image/x-compressed-xcf'],
        'xci' => ['application/x-nintendo-switch-xci', 'application/x-nx-xci'],
        'xcs' => ['application/calendar+xml'],
        'xdcf' => ['application/vnd.gov.sk.xmldatacontainer+xml'],
        'xdf' => ['application/mrb-consumer+xml', 'application/mrb-publish+xml', 'application/xcap-diff+xml'],
        'xdgapp' => ['application/vnd.flatpak', 'application/vnd.xdgapp'],
        'xdm' => ['application/vnd.syncml.dm+xml'],
        'xdp' => ['application/vnd.adobe.xdp+xml'],
        'xdssc' => ['application/dssc+xml'],
        'xdw' => ['application/vnd.fujixerox.docuworks'],
        'xel' => ['application/xcap-el+xml'],
        'xenc' => ['application/xenc+xml'],
        'xer' => ['application/patch-ops-error+xml', 'application/xcap-error+xml'],
        'xfdf' => ['application/vnd.adobe.xfdf', 'application/xfdf'],
        'xfdl' => ['application/vnd.xfdl'],
        'xhe' => ['audio/usac'],
        'xht' => ['application/xhtml+xml'],
        'xhtm' => ['application/vnd.pwg-xhtml-print+xml'],
        'xhtml' => ['application/xhtml+xml'],
        'xhvml' => ['application/xv+xml'],
        'xi' => ['audio/x-xi'],
        'xif' => ['image/vnd.xiff'],
        'xla' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xlam' => ['application/vnd.ms-excel.addin.macroenabled.12'],
        'xlc' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xld' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xlf' => ['application/x-xliff', 'application/x-xliff+xml', 'application/xliff+xml'],
        'xliff' => ['application/x-xliff', 'application/xliff+xml'],
        'xll' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xlm' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xlr' => ['application/vnd.ms-works'],
        'xls' => ['application/vnd.ms-excel', 'application/msexcel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xlsb' => ['application/vnd.ms-excel.sheet.binary.macroenabled.12'],
        'xlsm' => ['application/vnd.ms-excel.sheet.macroenabled.12'],
        'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
        'xlt' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xltm' => ['application/vnd.ms-excel.template.macroenabled.12'],
        'xltx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.template'],
        'xlw' => ['application/msexcel', 'application/vnd.ms-excel', 'application/x-msexcel', 'zz-application/zz-winassoc-xls'],
        'xm' => ['audio/x-xm', 'audio/xm'],
        'xmf' => ['audio/x-xmf', 'audio/xmf'],
        'xmi' => ['text/x-xmi'],
        'xml' => ['application/xml', 'text/xml'],
        'xns' => ['application/xcap-ns+xml'],
        'xo' => ['application/vnd.olpc-sugar'],
        'xop' => ['application/xop+xml'],
        'xpi' => ['application/x-xpinstall'],
        'xpl' => ['application/xproc+xml'],
        'xpm' => ['image/x-xpixmap', 'image/x-xpm'],
        'xpr' => ['application/vnd.is-xpr'],
        'xps' => ['application/vnd.ms-xpsdocument', 'application/xps'],
        'xpw' => ['application/vnd.intercon.formnet'],
        'xpx' => ['application/vnd.intercon.formnet'],
        'xsd' => ['application/xml', 'text/xml'],
        'xsf' => ['application/prs.xsf+xml'],
        'xsl' => ['application/xml', 'application/xslt+xml'],
        'xslfo' => ['text/x-xslfo'],
        'xslt' => ['application/xslt+xml'],
        'xsm' => ['application/vnd.syncml+xml'],
        'xspf' => ['application/x-xspf+xml', 'application/xspf+xml'],
        'xul' => ['application/vnd.mozilla.xul+xml'],
        'xvm' => ['application/xv+xml'],
        'xvml' => ['application/xv+xml'],
        'xwd' => ['image/x-xwindowdump'],
        'xyz' => ['chemical/x-xyz'],
        'xz' => ['application/x-xz'],
        'yaml' => ['application/yaml', 'application/x-yaml', 'text/x-yaml', 'text/yaml'],
        'yang' => ['application/yang'],
        'yin' => ['application/yin+xml'],
        'yml' => ['application/yaml', 'application/x-yaml', 'text/x-yaml', 'text/yaml'],
        'ymp' => ['text/x-suse-ymp'],
        'yt' => ['application/vnd.youtube.yt', 'video/vnd.youtube.yt'],
        'z1' => ['application/x-zmachine'],
        'z2' => ['application/x-zmachine'],
        'z3' => ['application/x-zmachine'],
        'z4' => ['application/x-zmachine'],
        'z5' => ['application/x-zmachine'],
        'z6' => ['application/x-zmachine'],
        'z64' => ['application/x-n64-rom'],
        'z7' => ['application/x-zmachine'],
        'z8' => ['application/x-zmachine'],
        'zabw' => ['application/x-abiword'],
        'zaz' => ['application/vnd.zzazz.deck+xml'],
        'zim' => ['application/x-openzim'],
        'zip' => ['application/zip', 'application/x-zip', 'application/x-zip-compressed'],
        'zipx' => ['application/x-zip', 'application/x-zip-compressed', 'application/zip'],
        'zir' => ['application/vnd.zul'],
        'zirz' => ['application/vnd.zul'],
        'zmm' => ['application/vnd.handheld-entertainment+xml'],
        'zoo' => ['application/x-zoo'],
        'zpaq' => ['application/x-zpaq'],
        'zsav' => ['application/x-spss-sav', 'application/x-spss-savefile'],
        'zst' => ['application/zstd'],
        'zz' => ['application/zlib'],
    ];
}
