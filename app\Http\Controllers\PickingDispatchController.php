<?php

namespace App\Http\Controllers;

use App\Models\PickingDispatch;
use Illuminate\Http\Request;

class PickingDispatchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(PickingDispatch $pickingDispatch)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PickingDispatch $pickingDispatch)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PickingDispatch $pickingDispatch)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PickingDispatch $pickingDispatch)
    {
        //
    }
}
