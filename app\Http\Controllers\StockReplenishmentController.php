<?php

namespace App\Http\Controllers;

use App\Models\StockReplenishment;
use Illuminate\Http\Request;

class StockReplenishmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(StockReplenishment $stockReplenishment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StockReplenishment $stockReplenishment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StockReplenishment $stockReplenishment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StockReplenishment $stockReplenishment)
    {
        //
    }
}
