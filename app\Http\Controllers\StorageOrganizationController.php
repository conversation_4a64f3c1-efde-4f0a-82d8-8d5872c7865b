<?php

namespace App\Http\Controllers;

use App\Models\StorageOrganization;
use Illuminate\Http\Request;

class StorageOrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(StorageOrganization $storageOrganization)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StorageOrganization $storageOrganization)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StorageOrganization $storageOrganization)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StorageOrganization $storageOrganization)
    {
        //
    }
}
